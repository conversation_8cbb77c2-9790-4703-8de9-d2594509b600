version: '3.8'

services:
  # Backend service
  backend:
    build:
      context: ./backend
      dockerfile: Dockerfile
    container_name: reading-practice-backend
    ports:
      - "8000:8000"
    environment:
      - DATABASE_URL=sqlite:///./data/reading_practice.db
      - SECRET_KEY=your-production-secret-key-change-this
      - ALGORITHM=HS256
      - ACCESS_TOKEN_EXPIRE_MINUTES=30
      - DEFAULT_OPENROUTER_API_KEY=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
    volumes:
      - backend_data:/app/data
    networks:
      - reading-practice-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8000/health"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Frontend service
  frontend:
    build:
      context: ./frontend
      dockerfile: Dockerfile
    container_name: reading-practice-frontend
    ports:
      - "80:80"
    depends_on:
      - backend
    networks:
      - reading-practice-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost"]
      interval: 30s
      timeout: 10s
      retries: 3

volumes:
  backend_data:
    driver: local

networks:
  reading-practice-network:
    driver: bridge