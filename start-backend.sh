#!/bin/bash

# Backend startup script
echo "🔧 Starting backend server..."

cd backend

# Check if conda environment exists and activate it
if conda env list | grep -q "reading-practice"; then
    echo "Activating conda environment..."
    eval "$(conda shell.bash hook)"
    conda activate reading-practice
    
    # Verify we're in the right environment
    echo "Python version: $(python --version)"
    echo "Python path: $(which python)"
    
    # Install dependencies if needed
    pip install -r requirements.txt
    
    # Start the server
    python -m uvicorn main:app --reload --host 0.0.0.0 --port 8001
else
    echo "❌ Conda environment 'reading-practice' not found."
    echo "Creating environment..."
    conda create -n reading-practice python=3.11 -y
    conda activate reading-practice
    pip install -r requirements.txt
    python -m uvicorn main:app --reload --host 0.0.0.0 --port 8001
fi