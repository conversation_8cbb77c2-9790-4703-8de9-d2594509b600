#!/bin/bash

# Development startup script for AI English Reading Practice Platform

echo "🚀 Starting AI English Reading Practice Platform in Development Mode"
echo "=================================================================="

# Check if conda is available
if ! command -v conda &> /dev/null; then
    echo "❌ Conda is not installed. Please install Miniconda or Anaconda first."
    exit 1
fi

# Backend setup
echo "📦 Setting up backend..."
cd backend

# Create conda environment if it doesn't exist
if ! conda env list | grep -q "reading-practice"; then
    echo "Creating conda environment..."
    conda create -n reading-practice python=3.11 -y
fi

# Activate environment
echo "Activating conda environment..."
eval "$(conda shell.bash hook)"
conda activate reading-practice

# Install dependencies
echo "Installing Python dependencies..."
pip install -r requirements.txt

# Start backend in background
echo "🔧 Starting backend server..."
uvicorn main:app --reload --host 0.0.0.0 --port 8000 &
BACKEND_PID=$!

cd ..

# Frontend setup
echo "📦 Setting up frontend..."
cd frontend

# Check if node is available
if ! command -v node &> /dev/null; then
    echo "❌ Node.js is not installed. Please install Node.js first."
    kill $BACKEND_PID
    exit 1
fi

# Install dependencies
echo "Installing Node.js dependencies..."
npm install

# Start frontend
echo "🎨 Starting frontend server..."
npm run dev &
FRONTEND_PID=$!

cd ..

echo ""
echo "✅ Development servers started successfully!"
echo "📱 Frontend: http://localhost:5173"
echo "🔧 Backend: http://localhost:8000"
echo "📚 API Docs: http://localhost:8000/docs"
echo ""
echo "Default admin credentials:"
echo "Username: admin"
echo "Password: admin"
echo ""
echo "Press Ctrl+C to stop all servers"

# Wait for interrupt
trap 'echo "Stopping servers..."; kill $BACKEND_PID $FRONTEND_PID; exit' INT
wait