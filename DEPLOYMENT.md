# Deployment Guide

This guide covers both development and production deployment of the AI English Reading Practice Platform.

## Prerequisites

### For Development
- Python 3.11+
- Node.js 18+
- <PERSON><PERSON> (Miniconda or Anaconda)

### For Production
- Docker
- Docker Compose

## Development Deployment

### Quick Start
```bash
# Make the script executable and run
chmod +x start-dev.sh
./start-dev.sh
```

### Manual Setup

#### Backend Setup
```bash
cd backend

# Create conda environment
conda create -n reading-practice python=3.11
conda activate reading-practice

# Install dependencies
pip install -r requirements.txt

# Start development server
uvicorn main:app --reload --host 0.0.0.0 --port 8000
```

#### Frontend Setup
```bash
cd frontend

# Install dependencies
npm install

# Start development server
npm run dev
```

### Access Points
- **Frontend**: http://localhost:5173
- **Backend API**: http://localhost:8000
- **API Documentation**: http://localhost:8000/docs

## Production Deployment

### Quick Start
```bash
# Make the script executable and run
chmod +x start-prod.sh
./start-prod.sh
```

### Manual Docker Compose
```bash
# Build and start services
docker-compose up --build -d

# View logs
docker-compose logs -f

# Stop services
docker-compose down
```

### Access Points
- **Application**: http://localhost
- **Backend API**: http://localhost:8000
- **API Documentation**: http://localhost:8000/docs

## Default Credentials

- **Username**: admin
- **Password**: admin

## Environment Variables

### Backend (.env)
```env
SECRET_KEY=your-secret-key-change-in-production
ALGORITHM=HS256
ACCESS_TOKEN_EXPIRE_MINUTES=30
DATABASE_URL=sqlite:///./reading_practice.db
DEFAULT_OPENROUTER_API_KEY=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
```

## Database

The application uses SQLite by default. The database file is automatically created on first startup with:
- Default admin user
- OpenRouter provider configuration
- Default GPT-4o model

## AI Configuration

The platform comes pre-configured with OpenRouter and GPT-4o model. Admins can:
- Add new AI providers
- Configure different models
- Set default models for generation and evaluation

## Troubleshooting

### Common Issues

1. **Port conflicts**: Ensure ports 8000 (backend) and 5173/80 (frontend) are available
2. **Database permissions**: Ensure the backend has write permissions for the database directory
3. **API key issues**: Verify the OpenRouter API key is valid and has sufficient credits

### Logs

#### Development
- Backend logs appear in the terminal
- Frontend logs appear in browser console

#### Production
```bash
# View all logs
docker-compose logs -f

# View specific service logs
docker-compose logs -f backend
docker-compose logs -f frontend
```

## Security Considerations

### For Production
1. Change the default admin password
2. Update the SECRET_KEY in environment variables
3. Use HTTPS in production
4. Secure the OpenRouter API key
5. Implement proper backup for the database

## Backup and Recovery

### Database Backup
```bash
# Development
cp backend/reading_practice.db backup/

# Production
docker-compose exec backend cp /app/data/reading_practice.db /app/backup/
```

## Scaling

For high-traffic deployments:
1. Use PostgreSQL instead of SQLite
2. Implement Redis for session management
3. Use a reverse proxy (Nginx) with load balancing
4. Consider container orchestration (Kubernetes)

## Monitoring

Recommended monitoring tools:
- Application logs
- Database performance
- API response times
- AI model usage and costs