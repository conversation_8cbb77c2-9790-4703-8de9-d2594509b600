#!/bin/bash

# Test script to verify the AI English Reading Practice Platform setup

echo "🧪 Testing AI English Reading Practice Platform Setup"
echo "=================================================="

# Check if all required files exist
echo "📁 Checking project structure..."

required_files=(
    "backend/main.py"
    "backend/requirements.txt"
    "backend/Dockerfile"
    "frontend/package.json"
    "frontend/vite.config.js"
    "frontend/Dockerfile"
    "docker-compose.yml"
)

missing_files=()

for file in "${required_files[@]}"; do
    if [ ! -f "$file" ]; then
        missing_files+=("$file")
    fi
done

if [ ${#missing_files[@]} -eq 0 ]; then
    echo "✅ All required files present"
else
    echo "❌ Missing files:"
    for file in "${missing_files[@]}"; do
        echo "   - $file"
    done
    exit 1
fi

# Check backend structure
echo "🔧 Checking backend structure..."
backend_dirs=("agents" "database" "routers" "schemas" "utils")
for dir in "${backend_dirs[@]}"; do
    if [ ! -d "backend/$dir" ]; then
        echo "❌ Missing backend directory: $dir"
        exit 1
    fi
done
echo "✅ Backend structure correct"

# Check frontend structure
echo "🎨 Checking frontend structure..."
frontend_dirs=("src/views" "src/stores" "src/services" "src/router")
for dir in "${frontend_dirs[@]}"; do
    if [ ! -d "frontend/$dir" ]; then
        echo "❌ Missing frontend directory: $dir"
        exit 1
    fi
done
echo "✅ Frontend structure correct"

# Check if Docker Compose is valid
echo "🐳 Validating Docker Compose configuration..."
if command -v docker-compose &> /dev/null; then
    if docker-compose config > /dev/null 2>&1; then
        echo "✅ Docker Compose configuration valid"
    else
        echo "❌ Docker Compose configuration invalid"
        exit 1
    fi
else
    echo "⚠️  Docker Compose not installed - skipping validation"
fi

# Check Python requirements
echo "🐍 Checking Python requirements..."
if [ -f "backend/requirements.txt" ]; then
    required_packages=("fastapi" "uvicorn" "sqlalchemy" "passlib" "python-jose" "openai")
    for package in "${required_packages[@]}"; do
        if ! grep -q "$package" backend/requirements.txt; then
            echo "❌ Missing Python package: $package"
            exit 1
        fi
    done
    echo "✅ Python requirements complete"
fi

# Check Node.js dependencies
echo "📦 Checking Node.js dependencies..."
if [ -f "frontend/package.json" ]; then
    required_packages=("vue" "vue-router" "pinia" "axios" "element-plus" "vite")
    for package in "${required_packages[@]}"; do
        if ! grep -q "\"$package\"" frontend/package.json; then
            echo "❌ Missing Node.js package: $package"
            exit 1
        fi
    done
    echo "✅ Node.js dependencies complete"
fi

echo ""
echo "🎉 Setup verification completed successfully!"
echo ""
echo "Next steps:"
echo "1. For development: ./start-dev.sh"
echo "2. For production: ./start-prod.sh"
echo "3. Access the application at http://localhost (production) or http://localhost:5173 (development)"
echo "4. Login with admin/admin credentials"