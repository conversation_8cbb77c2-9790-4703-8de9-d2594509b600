# API Documentation

This document provides detailed information about the AI English Reading Practice Platform API endpoints.

## Base URL
- Development: `http://localhost:8000/api/v1`
- Production: `http://your-domain.com/api/v1`

## Authentication

The API uses JWT (JSON Web Token) authentication. Include the token in the Authorization header:

```
Authorization: Bearer <your-jwt-token>
```

## Endpoints

### Authentication

#### POST /auth/login
Login with username and password.

**Request Body:**
```json
{
  "username": "admin",
  "password": "admin"
}
```

**Response:**
```json
{
  "access_token": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9...",
  "token_type": "bearer",
  "user": {
    "id": 1,
    "username": "admin",
    "role": "admin"
  }
}
```

#### GET /auth/me
Get current user information.

**Headers:** `Authorization: Bearer <token>`

**Response:**
```json
{
  "id": 1,
  "username": "admin",
  "role": "admin"
}
```

### Exercises

#### POST /exercises/
Generate a new reading exercise.

**Headers:** `Authorization: Bearer <token>`

**Request Body:**
```json
{
  "topic": "Technology",
  "difficulty": "Intermediate",
  "word_count": 300,
  "question_count": 5,
  "model_id": 1
}
```

**Response:**
```json
{
  "id": 1,
  "article": "Technology has revolutionized...",
  "questions": [
    {
      "question": "What is the main topic of the article?",
      "options": ["A) History", "B) Technology", "C) Science", "D) Art"],
      "correct_answer": "B",
      "explanation": "The article focuses on technological advancements."
    }
  ]
}
```

#### GET /exercises/{id}
Get a specific exercise.

**Headers:** `Authorization: Bearer <token>`

**Response:** Same as POST /exercises/

#### POST /exercises/{id}/submit
Submit answers for an exercise.

**Headers:** `Authorization: Bearer <token>`

**Request Body:**
```json
{
  "answers": ["B", "A", "C", "D", "B"]
}
```

**Response:**
```json
{
  "total_score": 85,
  "accuracy_rate": 0.8,
  "feedback": [
    {
      "question_number": 1,
      "user_answer": "B",
      "correct_answer": "B",
      "is_correct": true,
      "explanation": "Correct! The article discusses technology."
    }
  ],
  "overall_feedback": "Great job! You demonstrated strong comprehension skills."
}
```

#### GET /exercises/history
Get user's exercise history.

**Headers:** `Authorization: Bearer <token>`

**Response:**
```json
[
  {
    "id": 1,
    "created_at": "2024-01-01T10:00:00Z",
    "topic": "Technology",
    "difficulty": "Intermediate",
    "word_count": 300,
    "question_count": 5,
    "duration_seconds": 600,
    "accuracy_rate": 0.8
  }
]
```

### Settings (Admin Only)

#### GET /settings/providers
Get all LLM providers.

**Headers:** `Authorization: Bearer <admin-token>`

**Response:**
```json
[
  {
    "id": 1,
    "name": "OpenRouter",
    "api_base_url": "https://openrouter.ai/api/v1",
    "api_key": "sk-or-v1-****"
  }
]
```

#### POST /settings/providers
Create a new LLM provider.

**Headers:** `Authorization: Bearer <admin-token>`

**Request Body:**
```json
{
  "name": "Custom Provider",
  "api_base_url": "https://api.example.com/v1",
  "api_key": "your-api-key"
}
```

#### PUT /settings/providers/{id}
Update an LLM provider.

#### DELETE /settings/providers/{id}
Delete an LLM provider.

#### GET /settings/models
Get all LLM models.

**Response:**
```json
[
  {
    "id": 1,
    "provider_id": 1,
    "model_name": "openai/gpt-4o",
    "display_name": "GPT-4o (OpenRouter)",
    "is_default_generator": true,
    "is_default_judge": true,
    "provider": {
      "id": 1,
      "name": "OpenRouter",
      "api_base_url": "https://openrouter.ai/api/v1"
    }
  }
]
```

#### POST /settings/models
Create a new LLM model.

**Request Body:**
```json
{
  "provider_id": 1,
  "model_name": "openai/gpt-3.5-turbo",
  "display_name": "GPT-3.5 Turbo (OpenRouter)",
  "is_default_generator": false,
  "is_default_judge": false
}
```

#### PUT /settings/models/{id}
Update an LLM model.

#### DELETE /settings/models/{id}
Delete an LLM model.

#### POST /settings/models/defaults
Set default models.

**Request Body:**
```json
{
  "default_generator_id": 1,
  "default_judge_id": 1
}
```

### Helpers

#### POST /helpers/translate
Translate text.

**Headers:** `Authorization: Bearer <token>`

**Request Body:**
```json
{
  "text": "Hello world",
  "target_language": "Chinese"
}
```

**Response:**
```json
{
  "original_text": "Hello world",
  "translated_text": "你好世界",
  "target_language": "Chinese"
}
```

#### POST /helpers/ask
Ask AI for explanation.

**Headers:** `Authorization: Bearer <token>`

**Request Body:**
```json
{
  "text": "photosynthesis",
  "context": "The process by which plants make food"
}
```

**Response:**
```json
{
  "question": "photosynthesis",
  "answer": "Photosynthesis is the process by which plants convert light energy...",
  "context": "The process by which plants make food"
}
```

### Notes

#### POST /notes/
Create a new note.

**Headers:** `Authorization: Bearer <token>`

**Request Body:**
```json
{
  "selected_text": "photosynthesis",
  "note_content": "The process plants use to make food from sunlight",
  "exercise_id": 1
}
```

#### GET /notes/
Get user's notes with optional search.

**Headers:** `Authorization: Bearer <token>`

**Query Parameters:**
- `search`: Search term
- `exercise_id`: Filter by exercise
- `limit`: Maximum results (default: 50)
- `offset`: Skip results (default: 0)

#### PUT /notes/{id}
Update a note.

#### DELETE /notes/{id}
Delete a note.

## Error Responses

All endpoints return appropriate HTTP status codes:

- `200`: Success
- `201`: Created
- `400`: Bad Request
- `401`: Unauthorized
- `403`: Forbidden
- `404`: Not Found
- `422`: Validation Error
- `500`: Internal Server Error

**Error Response Format:**
```json
{
  "detail": "Error message description"
}
```

## Rate Limiting

The API implements rate limiting to prevent abuse:
- 100 requests per minute per user for general endpoints
- 10 requests per minute for AI-powered endpoints (generation, translation, explanations)

## OpenAPI Documentation

Interactive API documentation is available at:
- Development: http://localhost:8000/docs
- Production: http://your-domain.com:8000/docs