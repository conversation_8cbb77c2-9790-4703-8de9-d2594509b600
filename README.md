# AI English Reading Practice Platform

A comprehensive full-stack web application for English reading practice with AI-powered article generation, real-time assessment, and intelligent learning tools.

## Features

### Core Functionality
- **AI-Generated Content**: Create customized reading articles with adjustable topics, difficulty levels, and word counts
- **Interactive Reading**: Select text for instant translation, AI explanations, and note-taking
- **Intelligent Assessment**: AI-powered question generation and answer evaluation with detailed feedback
- **Progress Tracking**: Comprehensive analytics and performance history
- **Multi-User Support**: User and admin roles with appropriate permissions

### Advanced Features
- **Multi-Provider AI Support**: Configure multiple AI providers (OpenRouter, custom APIs)
- **Flexible Model Management**: Switch between different AI models for generation and evaluation
- **Real-Time Helpers**: In-context translation and AI explanations while reading
- **Note Management**: Save, search, and organize notes with full-text search
- **Responsive Design**: Works seamlessly on desktop and mobile devices

## Tech Stack

### Backend
- **Framework**: FastAPI (Python 3.11+)
- **Database**: SQLite with SQLAlchemy ORM
- **Authentication**: JWT tokens with bcrypt password hashing
- **AI Integration**: OpenAI-compatible API clients
- **Architecture**: Agent-based design pattern for AI tasks

### Frontend
- **Framework**: Vue 3 with Composition API
- **Build Tool**: Vite
- **State Management**: Pinia
- **UI Library**: Element Plus
- **HTTP Client**: Axios with interceptors

### DevOps & Testing
- **Containerization**: Docker & Docker Compose
- **Testing**: Playwright for E2E testing
- **Development**: Hot reload for both frontend and backend
- **Production**: Multi-stage Docker builds with Nginx

## Quick Start

### Option 1: Automated Setup (Recommended)

#### Development
```bash
# Clone the repository
git clone <repository-url>
cd ai-reading-practice-platform

# Start development environment
chmod +x start-dev.sh
./start-dev.sh
```

#### Production
```bash
# Start production environment
chmod +x start-prod.sh
./start-prod.sh
```

### Option 2: Manual Setup

#### Development Environment

**Prerequisites:**
- Python 3.11+
- Node.js 18+
- Conda (Miniconda or Anaconda)

**Backend Setup:**
```bash
cd backend
conda create -n reading-practice python=3.11
conda activate reading-practice
pip install -r requirements.txt
uvicorn main:app --reload --host 0.0.0.0 --port 8000
```

**Frontend Setup:**
```bash
cd frontend
npm install
npm run dev
```

#### Production Deployment

**Prerequisites:**
- Docker
- Docker Compose

```bash
docker-compose up --build -d
```

## Access Points

### Development
- **Frontend**: http://localhost:5173
- **Backend API**: http://localhost:8000
- **API Documentation**: http://localhost:8000/docs

### Production
- **Application**: http://localhost
- **Backend API**: http://localhost:8000
- **API Documentation**: http://localhost:8000/docs

## Default Credentials

- **Username**: `admin`
- **Password**: `admin`

> **Security Note**: Change the default admin password in production!

## Project Structure

```
ai-reading-practice-platform/
├── backend/                 # FastAPI backend
│   ├── agents/             # AI agent classes
│   ├── database/           # Database models and config
│   ├── routers/            # API route handlers
│   ├── schemas/            # Pydantic schemas
│   ├── utils/              # Utility functions
│   ├── main.py             # FastAPI app entry point
│   └── requirements.txt    # Python dependencies
├── frontend/               # Vue 3 frontend
│   ├── src/
│   │   ├── components/     # Vue components
│   │   ├── views/          # Page components
│   │   ├── stores/         # Pinia stores
│   │   ├── services/       # API services
│   │   └── router/         # Vue Router config
│   ├── tests/              # Playwright tests
│   └── package.json        # Node.js dependencies
├── docker-compose.yml      # Production deployment
├── start-dev.sh           # Development startup script
├── start-prod.sh          # Production startup script
└── README.md              # This file
```

## Usage Guide

### For Students
1. **Login** with your credentials
2. **Create Exercise**: Configure topic, difficulty, and length
3. **Read & Interact**: Select text for translations and explanations
4. **Answer Questions**: Complete comprehension questions
5. **Review Results**: Get detailed feedback and track progress
6. **Manage Notes**: Review saved translations and explanations

### For Administrators
1. **Access Settings**: Navigate to the admin settings panel
2. **Manage Providers**: Add/edit AI service providers
3. **Configure Models**: Set up different AI models
4. **Set Defaults**: Choose default models for generation and evaluation
5. **Monitor Usage**: Track system usage and performance

## Configuration

### Environment Variables

Create a `.env` file in the backend directory:

```env
# JWT Configuration
SECRET_KEY=your-secret-key-change-in-production
ALGORITHM=HS256
ACCESS_TOKEN_EXPIRE_MINUTES=30

# Database
DATABASE_URL=sqlite:///./reading_practice.db

# Default OpenRouter API Key
DEFAULT_OPENROUTER_API_KEY=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
```

### AI Provider Setup

The platform comes pre-configured with OpenRouter, but you can add custom providers:

1. Login as admin
2. Go to Settings → AI Providers
3. Add new provider with API endpoint and key
4. Configure models for the provider
5. Set default models for generation and evaluation

## Testing

### Frontend E2E Tests
```bash
cd frontend
npm run test
```

### Manual Testing
1. Start the development environment
2. Login with admin credentials
3. Create a new exercise
4. Test the reading interface
5. Verify note-taking functionality
6. Check admin settings panel

## API Documentation

Comprehensive API documentation is available at `/docs` when the backend is running. Key endpoints include:

- **Authentication**: `/api/v1/auth/*`
- **Exercises**: `/api/v1/exercises/*`
- **Settings**: `/api/v1/settings/*` (Admin only)
- **Helpers**: `/api/v1/helpers/*` (Translation, AI explanations)
- **Notes**: `/api/v1/notes/*`

See [API.md](API.md) for detailed endpoint documentation.

## Deployment

See [DEPLOYMENT.md](DEPLOYMENT.md) for comprehensive deployment instructions including:
- Development setup
- Production deployment
- Environment configuration
- Security considerations
- Scaling recommendations

## Security Features

- JWT-based authentication
- Password hashing with bcrypt
- Role-based access control
- API rate limiting
- CORS configuration
- Input validation and sanitization

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests if applicable
5. Submit a pull request

## License

This project is licensed under the MIT License - see the LICENSE file for details.

## Support

For issues and questions:
1. Check the troubleshooting section in [DEPLOYMENT.md](DEPLOYMENT.md)
2. Review the API documentation
3. Create an issue in the repository

## Acknowledgments

- OpenRouter for AI model access
- Element Plus for UI components
- FastAPI for the excellent Python web framework
- Vue.js team for the reactive frontend framework