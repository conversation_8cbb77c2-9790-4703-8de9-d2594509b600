"""
Notes schemas.
"""

from pydantic import BaseModel
from typing import Optional
from datetime import datetime

class NoteCreate(BaseModel):
    selected_text: str
    note_content: str
    exercise_id: Optional[int] = None

class NoteUpdate(BaseModel):
    note_content: str

class NoteResponse(BaseModel):
    id: int
    user_id: int
    exercise_id: Optional[int]
    created_at: datetime
    selected_text: str
    note_content: str
    
    class Config:
        from_attributes = True