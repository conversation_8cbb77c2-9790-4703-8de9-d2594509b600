"""
Exercise schemas.
"""

from pydantic import BaseModel
from typing import List, Optional, Dict, Any
from datetime import datetime

class ExerciseRequest(BaseModel):
    topic: str
    difficulty: str
    word_count: int
    question_count: int
    model_id: Optional[int] = None

class Question(BaseModel):
    question: str
    options: List[str]
    correct_answer: str
    explanation: str

class ExerciseResponse(BaseModel):
    id: int
    article: str
    questions: List[Question]

class SubmitAnswersRequest(BaseModel):
    answers: List[str]

class SubmitAnswersResponse(BaseModel):
    total_score: int
    accuracy_rate: float
    feedback: List[Dict[str, Any]]
    overall_feedback: str

class ExerciseHistory(BaseModel):
    id: int
    created_at: datetime
    topic: str
    difficulty: str
    word_count: int
    question_count: int
    duration_seconds: Optional[int]
    accuracy_rate: Optional[float]
    
    class Config:
        from_attributes = True