"""
Helper schemas.
"""

from pydantic import BaseModel
from typing import Optional

class TranslateRequest(BaseModel):
    text: str
    target_language: str = "Chinese"

class TranslateResponse(BaseModel):
    original_text: str
    translated_text: str
    target_language: str

class AskRequest(BaseModel):
    text: str
    context: Optional[str] = None

class AskResponse(BaseModel):
    question: str
    answer: str
    context: Optional[str] = None