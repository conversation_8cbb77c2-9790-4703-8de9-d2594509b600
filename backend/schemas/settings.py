"""
Settings schemas.
"""

from pydantic import BaseModel
from typing import Optional, List

class LLMProviderCreate(BaseModel):
    name: str
    api_base_url: str
    api_key: Optional[str] = None

class LLMProviderUpdate(BaseModel):
    name: Optional[str] = None
    api_base_url: Optional[str] = None
    api_key: Optional[str] = None

class LLMProviderResponse(BaseModel):
    id: int
    name: str
    api_base_url: str
    api_key: Optional[str] = None
    
    class Config:
        from_attributes = True

class LLMModelCreate(BaseModel):
    provider_id: int
    model_name: str
    display_name: str
    is_default_generator: bool = False
    is_default_judge: bool = False

class LLMModelUpdate(BaseModel):
    provider_id: Optional[int] = None
    model_name: Optional[str] = None
    display_name: Optional[str] = None
    is_default_generator: Optional[bool] = None
    is_default_judge: Optional[bool] = None

class LLMModelResponse(BaseModel):
    id: int
    provider_id: int
    model_name: str
    display_name: str
    is_default_generator: bool
    is_default_judge: bool
    provider: LLMProviderResponse
    
    class Config:
        from_attributes = True

class SetDefaultsRequest(BaseModel):
    default_generator_id: Optional[int] = None
    default_judge_id: Optional[int] = None