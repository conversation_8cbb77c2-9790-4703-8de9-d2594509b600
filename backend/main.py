"""
Main FastAPI application entry point for AI English Reading Practice Platform.
"""

from fastapi import <PERSON><PERSON><PERSON>, Depends, HTTPException, status
from fastapi.middleware.cors import CORSMiddleware
from fastapi.security import HTT<PERSON><PERSON>earer
from contextlib import asynccontextmanager
import logging

from database.database import engine, SessionLocal
from database.models import Base
from database.init_data import initialize_default_data
from routers import auth, exercises, settings, helpers, notes
from utils.auth import get_current_user

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Security
security = HTTPBearer()

@asynccontextmanager
async def lifespan(app: FastAPI):
    """Application lifespan events."""
    # Startup
    logger.info("Starting AI English Reading Practice Platform...")
    
    # Create database tables
    Base.metadata.create_all(bind=engine)
    logger.info("Database tables created")
    
    # Initialize default data
    db = SessionLocal()
    try:
        await initialize_default_data(db)
        logger.info("Default data initialized")
    finally:
        db.close()
    
    yield
    
    # Shutdown
    logger.info("Shutting down...")

# Create FastAPI app
app = FastAPI(
    title="AI English Reading Practice Platform",
    description="A comprehensive platform for English reading practice with AI-powered content generation",
    version="1.0.0",
    lifespan=lifespan
)

# CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["http://localhost:3000", "http://localhost:5173", "http://frontend"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Include routers
app.include_router(auth.router, prefix="/api/v1/auth", tags=["Authentication"])
app.include_router(exercises.router, prefix="/api/v1/exercises", tags=["Exercises"])
app.include_router(settings.router, prefix="/api/v1/settings", tags=["Settings"])
app.include_router(helpers.router, prefix="/api/v1/helpers", tags=["Helpers"])
app.include_router(notes.router, prefix="/api/v1/notes", tags=["Notes"])

@app.get("/")
async def root():
    """Root endpoint."""
    return {"message": "AI English Reading Practice Platform API"}

@app.get("/health")
async def health_check():
    """Health check endpoint."""
    return {"status": "healthy"}

if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="0.0.0.0", port=8000)