"""
Generator agent for creating reading articles and questions.
"""

from typing import Dict, Any
from agents.base_agent import BaseAgent

class GeneratorAgent(BaseAgent):
    """Agent responsible for generating reading articles and comprehension questions."""
    
    def get_system_prompt(self) -> str:
        """Get the system prompt for article and question generation."""
        return """You are an English test creator. Your task is to generate a reading comprehension exercise consisting of an article and multiple-choice questions.

IMPORTANT: You must respond with valid JSON in the following format:
{
    "article": "The complete article text here...",
    "questions": [
        {
            "question": "Question text here?",
            "options": ["A) Option 1", "B) Option 2", "C) Option 3", "D) Option 4"],
            "correct_answer": "A",
            "explanation": "Brief explanation of why this is correct"
        }
    ]
}

Guidelines for article creation:
- Write engaging, informative articles appropriate for the specified difficulty level
- Ensure the content is educational and interesting
- Use vocabulary and sentence structures appropriate for the difficulty level
- Include specific details that can be tested in comprehension questions

Guidelines for question creation:
- Create questions that test different types of comprehension (main idea, details, inference, vocabulary)
- Ensure all questions can be answered based on the article content
- Make incorrect options plausible but clearly wrong
- Provide brief explanations for correct answers
- Use clear, unambiguous language

Difficulty levels:
- Beginner: Simple vocabulary, short sentences, basic concepts
- Intermediate: Moderate vocabulary, varied sentence structures, more complex ideas
- Advanced: Rich vocabulary, complex sentences, sophisticated concepts

Always respond with valid JSON only, no additional text."""

    async def generate_exercise(
        self,
        topic: str,
        difficulty: str,
        word_count: int,
        question_count: int
    ) -> Dict[str, Any]:
        """Generate a complete reading exercise."""
        
        user_prompt = f"""Create a reading comprehension exercise with the following specifications:

Topic: {topic}
Difficulty Level: {difficulty}
Article Word Count: approximately {word_count} words
Number of Questions: {question_count}

Please generate an engaging article on the specified topic and create {question_count} multiple-choice questions that test comprehension of the article content."""

        return await self.generate_structured_response(user_prompt)