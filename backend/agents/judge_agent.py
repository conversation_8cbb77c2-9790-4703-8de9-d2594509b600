"""
Judge agent for evaluating user answers and providing feedback.
"""

from typing import Dict, Any, List
from agents.base_agent import BaseAgent

class JudgeAgent(BaseAgent):
    """Agent responsible for evaluating user answers and providing feedback."""
    
    def get_system_prompt(self) -> str:
        """Get the system prompt for answer evaluation."""
        return """You are an English comprehension evaluator. Your task is to evaluate user answers to reading comprehension questions and provide detailed feedback.

IMPORTANT: You must respond with valid JSON in the following format:
{
    "total_score": 85,
    "accuracy_rate": 0.85,
    "feedback": [
        {
            "question_number": 1,
            "user_answer": "A",
            "correct_answer": "B",
            "is_correct": false,
            "explanation": "Detailed explanation of the correct answer and why the user's answer was incorrect"
        }
    ],
    "overall_feedback": "Overall performance summary and suggestions for improvement"
}

Guidelines for evaluation:
- Calculate the total score as a percentage (0-100)
- Calculate accuracy rate as a decimal (0.0-1.0)
- Provide specific explanations for each question
- For incorrect answers, explain why the correct answer is right and why the user's answer is wrong
- For correct answers, provide positive reinforcement and brief explanation
- Give constructive overall feedback with specific suggestions for improvement
- Be encouraging while being honest about areas that need work

Always respond with valid JSON only, no additional text."""

    async def evaluate_answers(
        self,
        questions: List[Dict[str, Any]],
        user_answers: List[str],
        article: str
    ) -> Dict[str, Any]:
        """Evaluate user answers and provide feedback."""
        
        # Prepare the evaluation data
        evaluation_data = {
            "article": article,
            "questions_and_answers": []
        }
        
        for i, (question, user_answer) in enumerate(zip(questions, user_answers)):
            evaluation_data["questions_and_answers"].append({
                "question_number": i + 1,
                "question": question["question"],
                "options": question["options"],
                "correct_answer": question["correct_answer"],
                "user_answer": user_answer,
                "explanation": question.get("explanation", "")
            })
        
        user_prompt = f"""Please evaluate the following reading comprehension answers:

Article: {article}

Questions and Answers:
{evaluation_data["questions_and_answers"]}

Provide a detailed evaluation with scores, feedback for each question, and overall performance summary."""

        return await self.generate_structured_response(user_prompt)