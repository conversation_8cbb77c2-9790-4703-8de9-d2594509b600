"""
Base agent class for AI interactions.
"""

from abc import ABC, abstractmethod
from typing import Dict, Any, Optional
import openai
from openai import OpenAI

class BaseAgent(ABC):
    """Base class for all AI agents."""
    
    def __init__(self, api_base_url: str, api_key: str, model_name: str):
        """Initialize the agent with API configuration."""
        self.api_base_url = api_base_url
        self.api_key = api_key
        self.model_name = model_name
        
        # Initialize OpenAI client with custom base URL
        self.client = OpenAI(
            api_key=api_key,
            base_url=api_base_url
        )
    
    @abstractmethod
    def get_system_prompt(self) -> str:
        """Get the system prompt for this agent."""
        pass
    
    async def generate_response(self, user_prompt: str, **kwargs) -> str:
        """Generate a response using the AI model."""
        try:
            response = self.client.chat.completions.create(
                model=self.model_name,
                messages=[
                    {"role": "system", "content": self.get_system_prompt()},
                    {"role": "user", "content": user_prompt}
                ],
                temperature=kwargs.get("temperature", 0.7),
                max_tokens=kwargs.get("max_tokens", 2000)
            )
            return response.choices[0].message.content
        except Exception as e:
            raise Exception(f"AI model error: {str(e)}")
    
    async def generate_structured_response(self, user_prompt: str, **kwargs) -> Dict[str, Any]:
        """Generate a structured response (JSON) using the AI model."""
        try:
            response = self.client.chat.completions.create(
                model=self.model_name,
                messages=[
                    {"role": "system", "content": self.get_system_prompt()},
                    {"role": "user", "content": user_prompt}
                ],
                temperature=kwargs.get("temperature", 0.7),
                max_tokens=kwargs.get("max_tokens", 3000),
                response_format={"type": "json_object"}
            )
            
            import json
            return json.loads(response.choices[0].message.content)
        except Exception as e:
            raise Exception(f"AI model error: {str(e)}")