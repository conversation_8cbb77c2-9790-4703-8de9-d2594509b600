"""
Helper agent for translation and AI explanations.
"""

from agents.base_agent import BaseAgent

class HelperAgent(BaseAgent):
    """Agent responsible for providing translations and explanations."""
    
    def get_system_prompt(self) -> str:
        """Get the system prompt for helper functions."""
        return """You are an English learning assistant. Your role is to help users understand English text by providing translations, explanations, and context.

Guidelines:
- Provide clear, accurate translations when requested
- Explain vocabulary, grammar, and concepts in simple terms
- Give context and examples when helpful
- Be encouraging and supportive
- Adapt your explanations to the user's apparent level
- Keep responses concise but comprehensive

Always be helpful and educational in your responses."""

    async def translate_text(self, text: str, target_language: str = "Chinese") -> str:
        """Translate text to the target language."""
        
        user_prompt = f"""Please translate the following English text to {target_language}:

"{text}"

Provide a natural, accurate translation that preserves the meaning and tone of the original text."""

        return await self.generate_response(user_prompt)

    async def explain_text(self, text: str, context: str = "") -> str:
        """Provide an explanation of the text."""
        
        context_info = f"\n\nContext: {context}" if context else ""
        
        user_prompt = f"""Please explain the following English text in a clear and educational way:

"{text}"{context_info}

Provide explanations for:
- Difficult vocabulary or phrases
- Grammar structures
- Cultural references or idioms
- Overall meaning and significance

Make your explanation helpful for English learners."""

        return await self.generate_response(user_prompt)