"""
Exercises router.
"""

import json
from typing import List
from fastapi import APIRouter, Depends, HTTPException, status
from sqlalchemy.orm import Session
from database.database import get_db
from database.models import User, Exercise, LLMModel, LLMProvider
from schemas.exercises import (
    ExerciseRequest, ExerciseResponse, Question,
    SubmitAnswersRequest, SubmitAnswersResponse, ExerciseHistory
)
from utils.auth import get_current_user
from agents.generator_agent import GeneratorAgent
from agents.judge_agent import JudgeAgent

router = APIRouter()

@router.post("/", response_model=ExerciseResponse)
async def create_exercise(
    exercise_data: ExerciseRequest,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """Generate a new reading exercise."""
    
    # Determine which model to use
    if exercise_data.model_id:
        model = db.query(LLMModel).filter(LLMModel.id == exercise_data.model_id).first()
        if not model:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Model not found"
            )
    else:
        model = db.query(LLMModel).filter(LLMModel.is_default_generator == True).first()
        if not model:
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="No default generator model configured"
            )
    
    # Get provider details
    provider = db.query(LLMProvider).filter(LLMProvider.id == model.provider_id).first()
    if not provider:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Provider not found for model"
        )
    
    try:
        # Create generator agent
        generator = GeneratorAgent(
            api_base_url=provider.api_base_url,
            api_key=provider.api_key,
            model_name=model.model_name
        )
        
        # Generate exercise
        exercise_content = await generator.generate_exercise(
            topic=exercise_data.topic,
            difficulty=exercise_data.difficulty,
            word_count=exercise_data.word_count,
            question_count=exercise_data.question_count
        )
        
        # Save exercise to database
        exercise = Exercise(
            user_id=current_user.id,
            topic=exercise_data.topic,
            difficulty=exercise_data.difficulty,
            word_count=exercise_data.word_count,
            question_count=exercise_data.question_count,
            article=exercise_content["article"],
            questions_json=json.dumps(exercise_content["questions"])
        )
        
        db.add(exercise)
        db.commit()
        db.refresh(exercise)
        
        # Convert questions to response format
        questions = [Question(**q) for q in exercise_content["questions"]]
        
        return ExerciseResponse(
            id=exercise.id,
            article=exercise.article,
            questions=questions
        )
        
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to generate exercise: {str(e)}"
        )

@router.post("/{exercise_id}/submit", response_model=SubmitAnswersResponse)
async def submit_answers(
    exercise_id: int,
    answers_data: SubmitAnswersRequest,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """Submit answers for an exercise and get feedback."""
    
    # Get exercise
    exercise = db.query(Exercise).filter(
        Exercise.id == exercise_id,
        Exercise.user_id == current_user.id
    ).first()
    
    if not exercise:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Exercise not found"
        )
    
    # Get default judge model
    model = db.query(LLMModel).filter(LLMModel.is_default_judge == True).first()
    if not model:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="No default judge model configured"
        )
    
    # Get provider details
    provider = db.query(LLMProvider).filter(LLMProvider.id == model.provider_id).first()
    if not provider:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Provider not found for model"
        )
    
    try:
        # Parse questions from JSON
        questions = json.loads(exercise.questions_json)
        
        # Create judge agent
        judge = JudgeAgent(
            api_base_url=provider.api_base_url,
            api_key=provider.api_key,
            model_name=model.model_name
        )
        
        # Evaluate answers
        evaluation = await judge.evaluate_answers(
            questions=questions,
            user_answers=answers_data.answers,
            article=exercise.article
        )
        
        # Update exercise with results
        exercise.accuracy_rate = evaluation["accuracy_rate"]
        exercise.total_score = evaluation["total_score"]
        exercise.submission_results_json = json.dumps(evaluation)
        db.commit()

        return SubmitAnswersResponse(
            total_score=evaluation["total_score"],
            accuracy_rate=evaluation["accuracy_rate"],
            feedback=evaluation["feedback"],
            overall_feedback=evaluation["overall_feedback"]
        )
        
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to evaluate answers: {str(e)}"
        )

@router.get("/history", response_model=List[ExerciseHistory])
async def get_exercise_history(
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """Get user's exercise history."""
    exercises = db.query(Exercise).filter(
        Exercise.user_id == current_user.id
    ).order_by(Exercise.created_at.desc()).all()
    
    return [ExerciseHistory.from_orm(exercise) for exercise in exercises]

@router.get("/{exercise_id}", response_model=ExerciseResponse)
async def get_exercise(
    exercise_id: int,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """Get a specific exercise."""
    exercise = db.query(Exercise).filter(
        Exercise.id == exercise_id,
        Exercise.user_id == current_user.id
    ).first()
    
    if not exercise:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Exercise not found"
        )
    
    # Parse questions from JSON
    questions = json.loads(exercise.questions_json)
    questions_obj = [Question(**q) for q in questions]
    
    return ExerciseResponse(
        id=exercise.id,
        article=exercise.article,
        questions=questions_obj
    )

@router.get("/{exercise_id}/results", response_model=SubmitAnswersResponse)
async def get_exercise_results(
    exercise_id: int,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """Get submission results for a specific exercise."""
    exercise = db.query(Exercise).filter(
        Exercise.id == exercise_id,
        Exercise.user_id == current_user.id
    ).first()

    if not exercise:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Exercise not found"
        )

    if not exercise.submission_results_json:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="No submission results found for this exercise"
        )

    # Parse the stored results
    results = json.loads(exercise.submission_results_json)

    return SubmitAnswersResponse(
        total_score=results["total_score"],
        accuracy_rate=results["accuracy_rate"],
        feedback=results["feedback"],
        overall_feedback=results["overall_feedback"]
    )