"""
Notes router for managing user notes.
"""

from typing import List, Optional
from fastapi import APIRouter, Depends, HTTPException, status, Query
from sqlalchemy.orm import Session
from database.database import get_db
from database.models import User, Note
from schemas.notes import NoteCreate, NoteUpdate, NoteResponse
from utils.auth import get_current_user

router = APIRouter()

@router.post("/", response_model=NoteResponse)
async def create_note(
    note_data: NoteCreate,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """Create a new note."""
    note = Note(
        user_id=current_user.id,
        selected_text=note_data.selected_text,
        note_content=note_data.note_content,
        exercise_id=note_data.exercise_id
    )
    
    db.add(note)
    db.commit()
    db.refresh(note)
    
    return NoteResponse.from_orm(note)

@router.get("/", response_model=List[NoteResponse])
async def get_notes(
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db),
    search: Optional[str] = Query(None, description="Search in note content and selected text"),
    exercise_id: Optional[int] = Query(None, description="Filter by exercise ID"),
    limit: int = Query(50, description="Maximum number of notes to return"),
    offset: int = Query(0, description="Number of notes to skip")
):
    """Get user's notes with optional search and filtering."""
    query = db.query(Note).filter(Note.user_id == current_user.id)
    
    if exercise_id:
        query = query.filter(Note.exercise_id == exercise_id)
    
    if search:
        search_term = f"%{search}%"
        query = query.filter(
            (Note.note_content.like(search_term)) |
            (Note.selected_text.like(search_term))
        )
    
    notes = query.order_by(Note.created_at.desc()).offset(offset).limit(limit).all()
    
    return [NoteResponse.from_orm(note) for note in notes]

@router.get("/{note_id}", response_model=NoteResponse)
async def get_note(
    note_id: int,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """Get a specific note."""
    note = db.query(Note).filter(
        Note.id == note_id,
        Note.user_id == current_user.id
    ).first()
    
    if not note:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Note not found"
        )
    
    return NoteResponse.from_orm(note)

@router.put("/{note_id}", response_model=NoteResponse)
async def update_note(
    note_id: int,
    note_data: NoteUpdate,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """Update a note."""
    note = db.query(Note).filter(
        Note.id == note_id,
        Note.user_id == current_user.id
    ).first()
    
    if not note:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Note not found"
        )
    
    note.note_content = note_data.note_content
    db.commit()
    db.refresh(note)
    
    return NoteResponse.from_orm(note)

@router.delete("/{note_id}")
async def delete_note(
    note_id: int,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """Delete a note."""
    note = db.query(Note).filter(
        Note.id == note_id,
        Note.user_id == current_user.id
    ).first()
    
    if not note:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Note not found"
        )
    
    db.delete(note)
    db.commit()
    
    return {"message": "Note deleted successfully"}