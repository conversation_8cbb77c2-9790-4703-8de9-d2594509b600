"""
Helper router for translation and AI explanations.
"""

from fastapi import API<PERSON><PERSON><PERSON>, Depends, HTTPException, status
from sqlalchemy.orm import Session
from database.database import get_db
from database.models import User, LLMModel, LLMProvider
from schemas.helpers import TranslateRequest, TranslateResponse, AskRequest, AskResponse
from utils.auth import get_current_user
from agents.helper_agent import HelperAgent

router = APIRouter()

@router.post("/translate", response_model=TranslateResponse)
async def translate_text(
    translate_data: TranslateRequest,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """Translate text using AI."""
    
    # Get default generator model for translation
    model = db.query(LLMModel).filter(LLMModel.is_default_generator == True).first()
    if not model:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="No default model configured"
        )
    
    # Get provider details
    provider = db.query(LLMProvider).filter(LLMProvider.id == model.provider_id).first()
    if not provider:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Provider not found for model"
        )
    
    try:
        # Create helper agent
        helper = HelperAgent(
            api_base_url=provider.api_base_url,
            api_key=provider.api_key,
            model_name=model.model_name
        )
        
        # Translate text
        translated_text = await helper.translate_text(
            text=translate_data.text,
            target_language=translate_data.target_language
        )
        
        return TranslateResponse(
            original_text=translate_data.text,
            translated_text=translated_text,
            target_language=translate_data.target_language
        )
        
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Translation failed: {str(e)}"
        )

@router.post("/ask", response_model=AskResponse)
async def ask_ai(
    ask_data: AskRequest,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """Ask AI for explanation of text."""
    
    # Get default generator model for explanations
    model = db.query(LLMModel).filter(LLMModel.is_default_generator == True).first()
    if not model:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="No default model configured"
        )
    
    # Get provider details
    provider = db.query(LLMProvider).filter(LLMProvider.id == model.provider_id).first()
    if not provider:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Provider not found for model"
        )
    
    try:
        # Create helper agent
        helper = HelperAgent(
            api_base_url=provider.api_base_url,
            api_key=provider.api_key,
            model_name=model.model_name
        )
        
        # Get explanation
        explanation = await helper.explain_text(
            text=ask_data.text,
            context=ask_data.context or ""
        )
        
        return AskResponse(
            question=ask_data.text,
            answer=explanation,
            context=ask_data.context
        )
        
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"AI explanation failed: {str(e)}"
        )