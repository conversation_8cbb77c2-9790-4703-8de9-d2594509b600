"""
Settings router for admin configuration.
"""

from typing import List
from fastapi import APIRouter, Depends, HTTPException, status
from sqlalchemy.orm import Session
from database.database import get_db
from database.models import User, LLMProvider, LLMModel
from schemas.settings import (
    LLMProviderCreate, LLMProviderUpdate, LLMProviderResponse,
    LLMModelCreate, LLMModelUpdate, LLMModelResponse,
    SetDefaultsRequest
)
from utils.auth import get_current_admin_user

router = APIRouter()

# Provider endpoints
@router.get("/providers", response_model=List[LLMProviderResponse])
async def get_providers(
    current_user: User = Depends(get_current_admin_user),
    db: Session = Depends(get_db)
):
    """Get all LLM providers."""
    providers = db.query(LLMProvider).all()
    return [LLMProviderResponse.from_orm(provider) for provider in providers]

@router.post("/providers", response_model=LLMProviderResponse)
async def create_provider(
    provider_data: LL<PERSON>roviderCreate,
    current_user: User = Depends(get_current_admin_user),
    db: Session = Depends(get_db)
):
    """Create a new LLM provider."""
    # Check if provider name already exists
    existing_provider = db.query(LLMProvider).filter(
        LLMProvider.name == provider_data.name
    ).first()
    
    if existing_provider:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Provider name already exists"
        )
    
    provider = LLMProvider(**provider_data.dict())
    db.add(provider)
    db.commit()
    db.refresh(provider)
    
    return LLMProviderResponse.from_orm(provider)

@router.put("/providers/{provider_id}", response_model=LLMProviderResponse)
async def update_provider(
    provider_id: int,
    provider_data: LLMProviderUpdate,
    current_user: User = Depends(get_current_admin_user),
    db: Session = Depends(get_db)
):
    """Update an LLM provider."""
    provider = db.query(LLMProvider).filter(LLMProvider.id == provider_id).first()
    
    if not provider:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Provider not found"
        )
    
    # Update fields
    update_data = provider_data.dict(exclude_unset=True)
    for field, value in update_data.items():
        setattr(provider, field, value)
    
    db.commit()
    db.refresh(provider)
    
    return LLMProviderResponse.from_orm(provider)

@router.delete("/providers/{provider_id}")
async def delete_provider(
    provider_id: int,
    current_user: User = Depends(get_current_admin_user),
    db: Session = Depends(get_db)
):
    """Delete an LLM provider."""
    provider = db.query(LLMProvider).filter(LLMProvider.id == provider_id).first()
    
    if not provider:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Provider not found"
        )
    
    # Check if provider has models
    models_count = db.query(LLMModel).filter(LLMModel.provider_id == provider_id).count()
    if models_count > 0:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Cannot delete provider with associated models"
        )
    
    db.delete(provider)
    db.commit()
    
    return {"message": "Provider deleted successfully"}

# Model endpoints
@router.get("/models", response_model=List[LLMModelResponse])
async def get_models(
    current_user: User = Depends(get_current_admin_user),
    db: Session = Depends(get_db)
):
    """Get all LLM models."""
    models = db.query(LLMModel).join(LLMProvider).all()
    return [LLMModelResponse.from_orm(model) for model in models]

@router.post("/models", response_model=LLMModelResponse)
async def create_model(
    model_data: LLMModelCreate,
    current_user: User = Depends(get_current_admin_user),
    db: Session = Depends(get_db)
):
    """Create a new LLM model."""
    # Check if provider exists
    provider = db.query(LLMProvider).filter(
        LLMProvider.id == model_data.provider_id
    ).first()
    
    if not provider:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Provider not found"
        )
    
    # If setting as default, unset other defaults
    if model_data.is_default_generator:
        db.query(LLMModel).filter(LLMModel.is_default_generator == True).update(
            {"is_default_generator": False}
        )
    
    if model_data.is_default_judge:
        db.query(LLMModel).filter(LLMModel.is_default_judge == True).update(
            {"is_default_judge": False}
        )
    
    model = LLMModel(**model_data.dict())
    db.add(model)
    db.commit()
    db.refresh(model)
    
    return LLMModelResponse.from_orm(model)

@router.put("/models/{model_id}", response_model=LLMModelResponse)
async def update_model(
    model_id: int,
    model_data: LLMModelUpdate,
    current_user: User = Depends(get_current_admin_user),
    db: Session = Depends(get_db)
):
    """Update an LLM model."""
    model = db.query(LLMModel).filter(LLMModel.id == model_id).first()
    
    if not model:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Model not found"
        )
    
    # If setting as default, unset other defaults
    update_data = model_data.dict(exclude_unset=True)
    
    if update_data.get("is_default_generator"):
        db.query(LLMModel).filter(
            LLMModel.is_default_generator == True,
            LLMModel.id != model_id
        ).update({"is_default_generator": False})
    
    if update_data.get("is_default_judge"):
        db.query(LLMModel).filter(
            LLMModel.is_default_judge == True,
            LLMModel.id != model_id
        ).update({"is_default_judge": False})
    
    # Update fields
    for field, value in update_data.items():
        setattr(model, field, value)
    
    db.commit()
    db.refresh(model)
    
    return LLMModelResponse.from_orm(model)

@router.delete("/models/{model_id}")
async def delete_model(
    model_id: int,
    current_user: User = Depends(get_current_admin_user),
    db: Session = Depends(get_db)
):
    """Delete an LLM model."""
    model = db.query(LLMModel).filter(LLMModel.id == model_id).first()
    
    if not model:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Model not found"
        )
    
    db.delete(model)
    db.commit()
    
    return {"message": "Model deleted successfully"}

@router.post("/models/defaults")
async def set_defaults(
    defaults_data: SetDefaultsRequest,
    current_user: User = Depends(get_current_admin_user),
    db: Session = Depends(get_db)
):
    """Set default generator and judge models."""
    
    if defaults_data.default_generator_id:
        # Unset all generator defaults
        db.query(LLMModel).filter(LLMModel.is_default_generator == True).update(
            {"is_default_generator": False}
        )
        
        # Set new default
        generator_model = db.query(LLMModel).filter(
            LLMModel.id == defaults_data.default_generator_id
        ).first()
        
        if not generator_model:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Generator model not found"
            )
        
        generator_model.is_default_generator = True
    
    if defaults_data.default_judge_id:
        # Unset all judge defaults
        db.query(LLMModel).filter(LLMModel.is_default_judge == True).update(
            {"is_default_judge": False}
        )
        
        # Set new default
        judge_model = db.query(LLMModel).filter(
            LLMModel.id == defaults_data.default_judge_id
        ).first()
        
        if not judge_model:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Judge model not found"
            )
        
        judge_model.is_default_judge = True
    
    db.commit()
    
    return {"message": "Default models updated successfully"}