"""
Authentication router.
"""

from datetime import timedelta
from fastapi import <PERSON><PERSON>outer, Depends, HTTPException, status
from fastapi.security import OAuth2PasswordR<PERSON><PERSON>Form
from sqlalchemy.orm import Session
from database.database import get_db
from database.models import User
from schemas.auth import LoginRequest, LoginResponse, UserInfo
from utils.auth import authenticate_user, create_access_token, get_current_user, ACCESS_TOKEN_EXPIRE_MINUTES

router = APIRouter()

@router.post("/login", response_model=LoginResponse)
async def login(login_data: LoginRequest, db: Session = Depends(get_db)):
    """Authenticate user and return JWT token."""
    user = authenticate_user(db, login_data.username, login_data.password)
    if not user:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Incorrect username or password",
            headers={"WWW-Authenticate": "Bearer"},
        )
    
    access_token_expires = timedelta(minutes=ACCESS_TOKEN_EXPIRE_MINUTES)
    access_token = create_access_token(
        data={"sub": user.username}, expires_delta=access_token_expires
    )
    
    return LoginResponse(
        access_token=access_token,
        token_type="bearer",
        user={
            "id": user.id,
            "username": user.username,
            "role": user.role
        }
    )

@router.get("/me", response_model=UserInfo)
async def read_users_me(current_user: User = Depends(get_current_user)):
    """Get current user information."""
    return UserInfo(
        id=current_user.id,
        username=current_user.username,
        role=current_user.role
    )