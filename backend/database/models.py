"""
SQLAlchemy models for the AI English Reading Practice Platform.
"""

from sqlalchemy import Column, Integer, String, Text, Boolean, REAL, TIMESTAMP, ForeignKey
from sqlalchemy.orm import relationship
from sqlalchemy.sql import func
from database.database import Base

class User(Base):
    """User model."""
    __tablename__ = "users"
    
    id = Column(Integer, primary_key=True, autoincrement=True)
    username = Column(String, unique=True, nullable=False)
    password_hash = Column(Text, nullable=False)
    role = Column(String, nullable=False, default="user")  # 'user' or 'admin'
    
    # Relationships
    exercises = relationship("Exercise", back_populates="user")
    notes = relationship("Note", back_populates="user")

class LLMProvider(Base):
    """LLM Provider model."""
    __tablename__ = "llm_providers"
    
    id = Column(Integer, primary_key=True, autoincrement=True)
    name = Column(String, unique=True, nullable=False)  # e.g., "OpenRouter", "My-Local-LLM"
    api_base_url = Column(Text, nullable=False)
    api_key = Column(Text)
    
    # Relationships
    models = relationship("LLMModel", back_populates="provider")

class LLMModel(Base):
    """LLM Model model."""
    __tablename__ = "llm_models"
    
    id = Column(Integer, primary_key=True, autoincrement=True)
    provider_id = Column(Integer, ForeignKey("llm_providers.id"), nullable=False)
    model_name = Column(String, nullable=False)  # The actual name used in the API call
    display_name = Column(String, nullable=False)  # The name shown to the user
    is_default_generator = Column(Boolean, default=False)
    is_default_judge = Column(Boolean, default=False)
    
    # Relationships
    provider = relationship("LLMProvider", back_populates="models")

class Exercise(Base):
    """Exercise model."""
    __tablename__ = "exercises"

    id = Column(Integer, primary_key=True, autoincrement=True)
    user_id = Column(Integer, ForeignKey("users.id"), nullable=False)
    created_at = Column(TIMESTAMP, default=func.current_timestamp())
    topic = Column(Text)
    difficulty = Column(String, nullable=False)
    word_count = Column(Integer)
    question_count = Column(Integer)
    duration_seconds = Column(Integer)
    accuracy_rate = Column(REAL)  # e.g., 0.8 for 80%
    total_score = Column(Integer)  # Total score out of 100
    submission_results_json = Column(Text)  # Store complete submission results as JSON
    article = Column(Text, nullable=False)
    questions_json = Column(Text, nullable=False)  # Store questions and correct answers as JSON

    # Relationships
    user = relationship("User", back_populates="exercises")
    notes = relationship("Note", back_populates="exercise")

class Note(Base):
    """Note model."""
    __tablename__ = "notes"
    
    id = Column(Integer, primary_key=True, autoincrement=True)
    user_id = Column(Integer, ForeignKey("users.id"), nullable=False)
    exercise_id = Column(Integer, ForeignKey("exercises.id"))
    created_at = Column(TIMESTAMP, default=func.current_timestamp())
    selected_text = Column(Text, nullable=False)
    note_content = Column(Text, nullable=False)
    
    # Relationships
    user = relationship("User", back_populates="notes")
    exercise = relationship("Exercise", back_populates="notes")