"""
Initialize default data for the application.
"""

import os
from sqlalchemy.orm import Session
from passlib.context import <PERSON>ptContext
from database.models import User, <PERSON>MProvider, LLMModel
from dotenv import load_dotenv

load_dotenv()

# Password hashing
pwd_context = CryptContext(schemes=["bcrypt"], deprecated="auto")

async def initialize_default_data(db: Session):
    """Initialize default data on first startup."""
    
    # Create admin user if doesn't exist
    admin_user = db.query(User).filter(User.username == "admin").first()
    if not admin_user:
        admin_password_hash = pwd_context.hash("admin")
        admin_user = User(
            username="admin",
            password_hash=admin_password_hash,
            role="admin"
        )
        db.add(admin_user)
        db.commit()
        print("Created default admin user (username: admin, password: admin)")
    
    # Create OpenRouter provider if doesn't exist
    openrouter_provider = db.query(LLMProvider).filter(LLMProvider.name == "OpenRouter").first()
    if not openrouter_provider:
        default_api_key = os.getenv("DEFAULT_OPENROUTER_API_KEY")
        openrouter_provider = LLMProvider(
            name="OpenRouter",
            api_base_url="https://openrouter.ai/api/v1",
            api_key=default_api_key
        )
        db.add(openrouter_provider)
        db.commit()
        db.refresh(openrouter_provider)
        print("Created default OpenRouter provider")
        
        # Add default model under OpenRouter
        default_model = LLMModel(
            provider_id=openrouter_provider.id,
            model_name="openai/gpt-4o",
            display_name="GPT-4o (OpenRouter)",
            is_default_generator=True,
            is_default_judge=True
        )
        db.add(default_model)
        db.commit()
        print("Created default GPT-4o model")
    
    print("Default data initialization completed")