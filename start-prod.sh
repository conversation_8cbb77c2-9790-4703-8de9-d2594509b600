#!/bin/bash

# Production startup script for AI English Reading Practice Platform

echo "🚀 Starting AI English Reading Practice Platform in Production Mode"
echo "=================================================================="

# Check if docker-compose is available
if ! command -v docker-compose &> /dev/null; then
    echo "❌ Docker Compose is not installed. Please install Docker and Docker Compose first."
    exit 1
fi

# Build and start services
echo "🔨 Building and starting services..."
docker-compose up --build -d

# Wait a moment for services to start
echo "⏳ Waiting for services to start..."
sleep 10

# Check service status
echo "📊 Service Status:"
docker-compose ps

echo ""
echo "✅ Production deployment started successfully!"
echo "🌐 Application: http://localhost"
echo "🔧 Backend API: http://localhost:8000"
echo "📚 API Documentation: http://localhost:8000/docs"
echo ""
echo "Default admin credentials:"
echo "Username: admin"
echo "Password: admin"
echo ""
echo "To stop the application, run: docker-compose down"
echo "To view logs, run: docker-compose logs -f"