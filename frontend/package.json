{"name": "ai-reading-practice-frontend", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "preview": "vite preview", "test": "playwright test", "test:ui": "playwright test --ui"}, "dependencies": {"vue": "^3.3.8", "vue-router": "^4.2.5", "pinia": "^2.1.7", "axios": "^1.6.2", "element-plus": "^2.4.4", "@element-plus/icons-vue": "^2.3.1"}, "devDependencies": {"@vitejs/plugin-vue": "^4.5.0", "vite": "^5.0.0", "@playwright/test": "^1.40.0", "unplugin-auto-import": "^0.17.2", "unplugin-vue-components": "^0.25.2"}}