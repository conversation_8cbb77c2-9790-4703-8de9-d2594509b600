import { test, expect } from '@playwright/test';

test.describe('AI English Reading Practice Platform', () => {
  test('should display login page', async ({ page }) => {
    await page.goto('/');
    
    // Should redirect to login page
    await expect(page).toHaveURL('/login');
    
    // Should display login form
    await expect(page.locator('h1')).toContainText('AI English Reading Practice');
    await expect(page.locator('input[placeholder="Username"]')).toBeVisible();
    await expect(page.locator('input[placeholder="Password"]')).toBeVisible();
  });

  test('should login with admin credentials', async ({ page }) => {
    await page.goto('/login');
    
    // Fill login form
    await page.fill('input[placeholder="Username"]', 'admin');
    await page.fill('input[placeholder="Password"]', 'admin');
    
    // Submit form
    await page.click('button:has-text("Sign In")');
    
    // Should redirect to dashboard
    await expect(page).toHaveURL('/dashboard');
    await expect(page.locator('h1')).toContainText('Welcome back, admin!');
  });

  test('should navigate to exercise configuration', async ({ page }) => {
    // Login first
    await page.goto('/login');
    await page.fill('input[placeholder="Username"]', 'admin');
    await page.fill('input[placeholder="Password"]', 'admin');
    await page.click('button:has-text("Sign In")');
    
    // Navigate to config
    await page.click('text=New Exercise');
    await expect(page).toHaveURL('/config');
    await expect(page.locator('h2')).toContainText('Create New Reading Exercise');
  });

  test('should access admin settings', async ({ page }) => {
    // Login as admin
    await page.goto('/login');
    await page.fill('input[placeholder="Username"]', 'admin');
    await page.fill('input[placeholder="Password"]', 'admin');
    await page.click('button:has-text("Sign In")');
    
    // Navigate to settings
    await page.click('text=Settings');
    await expect(page).toHaveURL('/settings');
    await expect(page.locator('h1')).toContainText('System Settings');
  });
});