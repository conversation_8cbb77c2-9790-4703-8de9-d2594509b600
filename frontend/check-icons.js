// Simple script to check available Element Plus icons
import * as ElementPlusIconsVue from '@element-plus/icons-vue'

console.log('Available Element Plus Icons:')
console.log(Object.keys(ElementPlusIconsVue).sort())

// Check specific icons we're using
const iconsToCheck = [
  'House', 'Edit', 'Document', 'Setting', 'User', 'ArrowDown', 'SwitchButton',
  'Timer', 'InfoFilled', 'Switch', 'ChatLineRound', 'Loading',
  'Star', 'DataAnalysis', 'Plus', 'View', 'Search', 'Delete', 'Lock'
]

console.log('\nChecking our icons:')
iconsToCheck.forEach(icon => {
  if (ElementPlusIconsVue[icon]) {
    console.log(`✅ ${icon} - Available`)
  } else {
    console.log(`❌ ${icon} - NOT AVAILABLE`)
  }
})