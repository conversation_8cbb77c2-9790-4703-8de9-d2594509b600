import api from './api'

export const helperService = {
  // Translate text
  async translateText(text, targetLanguage = 'Chinese') {
    const response = await api.post('/helpers/translate', {
      text,
      target_language: targetLanguage
    })
    return response.data
  },

  // Ask AI for explanation
  async askAI(text, context = null) {
    const response = await api.post('/helpers/ask', {
      text,
      context
    })
    return response.data
  }
}