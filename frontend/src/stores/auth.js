import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import api from '../services/api'

export const useAuthStore = defineStore('auth', () => {
  // State
  const token = ref(localStorage.getItem('token') || null)
  const user = ref(JSON.parse(localStorage.getItem('user') || 'null'))
  const initialized = ref(false)

  // Getters
  const isAuthenticated = computed(() => !!token.value)

  // Actions
  const initializeAuth = () => {
    const storedToken = localStorage.getItem('token')
    const storedUser = localStorage.getItem('user')
    
    if (storedToken && storedUser) {
      token.value = storedToken
      user.value = JSON.parse(storedUser)
      // Set token in API headers
      api.defaults.headers.common['Authorization'] = `Bearer ${storedToken}`
    }
    
    initialized.value = true
  }

  const login = async (credentials) => {
    try {
      const response = await api.post('/auth/login', credentials)
      const { access_token, user: userData } = response.data
      
      // Store token and user data
      token.value = access_token
      user.value = userData
      
      // Persist to localStorage
      localStorage.setItem('token', access_token)
      localStorage.setItem('user', JSON.stringify(userData))
      
      // Set token in API headers
      api.defaults.headers.common['Authorization'] = `Bearer ${access_token}`
      
      return response.data
    } catch (error) {
      throw error
    }
  }

  const logout = async () => {
    // Clear state
    token.value = null
    user.value = null
    
    // Clear localStorage
    localStorage.removeItem('token')
    localStorage.removeItem('user')
    
    // Remove token from API headers
    delete api.defaults.headers.common['Authorization']
  }

  const getCurrentUser = async () => {
    try {
      const response = await api.get('/auth/me')
      user.value = response.data
      localStorage.setItem('user', JSON.stringify(response.data))
      return response.data
    } catch (error) {
      // If token is invalid, logout
      await logout()
      throw error
    }
  }

  return {
    token,
    user,
    initialized,
    isAuthenticated,
    initializeAuth,
    login,
    logout,
    getCurrentUser
  }
})