import { defineStore } from 'pinia'
import { ref } from 'vue'
import api from '../services/api'

export const useExerciseStore = defineStore('exercise', () => {
  // State
  const currentExercise = ref(null)
  const exerciseHistory = ref([])
  const loading = ref(false)

  // Actions
  const generateExercise = async (config) => {
    loading.value = true
    try {
      const response = await api.post('/exercises/', config)
      currentExercise.value = response.data
      return response.data
    } catch (error) {
      throw error
    } finally {
      loading.value = false
    }
  }

  const getExercise = async (id) => {
    loading.value = true
    try {
      const response = await api.get(`/exercises/${id}`)
      currentExercise.value = response.data
      return response.data
    } catch (error) {
      throw error
    } finally {
      loading.value = false
    }
  }

  const submitAnswers = async (exerciseId, answers) => {
    loading.value = true
    try {
      const response = await api.post(`/exercises/${exerciseId}/submit`, {
        answers
      })
      return response.data
    } catch (error) {
      throw error
    } finally {
      loading.value = false
    }
  }

  const getExerciseHistory = async () => {
    loading.value = true
    try {
      const response = await api.get('/exercises/history')
      exerciseHistory.value = response.data
      return response.data
    } catch (error) {
      throw error
    } finally {
      loading.value = false
    }
  }

  const getExerciseResults = async (exerciseId) => {
    loading.value = true
    try {
      const response = await api.get(`/exercises/${exerciseId}/results`)
      return response.data
    } catch (error) {
      throw error
    } finally {
      loading.value = false
    }
  }

  const clearCurrentExercise = () => {
    currentExercise.value = null
  }

  return {
    currentExercise,
    exerciseHistory,
    loading,
    generateExercise,
    getExercise,
    submitAnswers,
    getExerciseResults,
    getExerciseHistory,
    clearCurrentExercise
  }
})