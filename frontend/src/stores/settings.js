import { defineStore } from 'pinia'
import { ref } from 'vue'
import api from '../services/api'

export const useSettingsStore = defineStore('settings', () => {
  // State
  const providers = ref([])
  const models = ref([])
  const loading = ref(false)

  // Actions
  const getProviders = async () => {
    loading.value = true
    try {
      const response = await api.get('/settings/providers')
      providers.value = response.data
      return response.data
    } catch (error) {
      throw error
    } finally {
      loading.value = false
    }
  }

  const createProvider = async (providerData) => {
    loading.value = true
    try {
      const response = await api.post('/settings/providers', providerData)
      providers.value.push(response.data)
      return response.data
    } catch (error) {
      throw error
    } finally {
      loading.value = false
    }
  }

  const updateProvider = async (providerId, providerData) => {
    loading.value = true
    try {
      const response = await api.put(`/settings/providers/${providerId}`, providerData)
      const index = providers.value.findIndex(p => p.id === providerId)
      if (index !== -1) {
        providers.value[index] = response.data
      }
      return response.data
    } catch (error) {
      throw error
    } finally {
      loading.value = false
    }
  }

  const deleteProvider = async (providerId) => {
    loading.value = true
    try {
      await api.delete(`/settings/providers/${providerId}`)
      providers.value = providers.value.filter(p => p.id !== providerId)
    } catch (error) {
      throw error
    } finally {
      loading.value = false
    }
  }

  const getModels = async () => {
    loading.value = true
    try {
      const response = await api.get('/settings/models')
      models.value = response.data
      return response.data
    } catch (error) {
      throw error
    } finally {
      loading.value = false
    }
  }

  const createModel = async (modelData) => {
    loading.value = true
    try {
      const response = await api.post('/settings/models', modelData)
      models.value.push(response.data)
      return response.data
    } catch (error) {
      throw error
    } finally {
      loading.value = false
    }
  }

  const updateModel = async (modelId, modelData) => {
    loading.value = true
    try {
      const response = await api.put(`/settings/models/${modelId}`, modelData)
      const index = models.value.findIndex(m => m.id === modelId)
      if (index !== -1) {
        models.value[index] = response.data
      }
      return response.data
    } catch (error) {
      throw error
    } finally {
      loading.value = false
    }
  }

  const deleteModel = async (modelId) => {
    loading.value = true
    try {
      await api.delete(`/settings/models/${modelId}`)
      models.value = models.value.filter(m => m.id !== modelId)
    } catch (error) {
      throw error
    } finally {
      loading.value = false
    }
  }

  const setDefaults = async (defaultsData) => {
    loading.value = true
    try {
      await api.post('/settings/models/defaults', defaultsData)
      // Refresh models to get updated default flags
      await getModels()
    } catch (error) {
      throw error
    } finally {
      loading.value = false
    }
  }

  return {
    providers,
    models,
    loading,
    getProviders,
    createProvider,
    updateProvider,
    deleteProvider,
    getModels,
    createModel,
    updateModel,
    deleteModel,
    setDefaults
  }
})