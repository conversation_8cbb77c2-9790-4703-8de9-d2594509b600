import { defineStore } from 'pinia'
import { ref } from 'vue'
import api from '../services/api'

export const useNotesStore = defineStore('notes', () => {
  // State
  const notes = ref([])
  const loading = ref(false)

  // Actions
  const createNote = async (noteData) => {
    loading.value = true
    try {
      const response = await api.post('/notes/', noteData)
      notes.value.unshift(response.data)
      return response.data
    } catch (error) {
      throw error
    } finally {
      loading.value = false
    }
  }

  const getNotes = async (params = {}) => {
    loading.value = true
    try {
      const response = await api.get('/notes/', { params })
      notes.value = response.data
      return response.data
    } catch (error) {
      throw error
    } finally {
      loading.value = false
    }
  }

  const updateNote = async (noteId, noteData) => {
    loading.value = true
    try {
      const response = await api.put(`/notes/${noteId}`, noteData)
      const index = notes.value.findIndex(note => note.id === noteId)
      if (index !== -1) {
        notes.value[index] = response.data
      }
      return response.data
    } catch (error) {
      throw error
    } finally {
      loading.value = false
    }
  }

  const deleteNote = async (noteId) => {
    loading.value = true
    try {
      await api.delete(`/notes/${noteId}`)
      notes.value = notes.value.filter(note => note.id !== noteId)
    } catch (error) {
      throw error
    } finally {
      loading.value = false
    }
  }

  const searchNotes = async (searchTerm) => {
    loading.value = true
    try {
      const response = await api.get('/notes/', {
        params: { search: searchTerm }
      })
      notes.value = response.data
      return response.data
    } catch (error) {
      throw error
    } finally {
      loading.value = false
    }
  }

  return {
    notes,
    loading,
    createNote,
    getNotes,
    updateNote,
    deleteNote,
    searchNotes
  }
})