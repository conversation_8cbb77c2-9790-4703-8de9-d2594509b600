<template>
  <div id="app">
    <el-container class="app-container">
      <!-- Header -->
      <el-header v-if="authStore.isAuthenticated" class="app-header">
        <div class="header-content">
          <div class="logo">
            <h2>AI Reading Practice</h2>
          </div>
          <div class="nav-menu">
            <el-menu
              mode="horizontal"
              :default-active="$route.path"
              router
              class="nav-menu-items"
            >
              <el-menu-item index="/dashboard">
                <el-icon><House /></el-icon>
                Dashboard
              </el-menu-item>
              <el-menu-item index="/config">
                <el-icon><Edit /></el-icon>
                New Exercise
              </el-menu-item>
              <el-menu-item index="/notes">
                <el-icon><Document /></el-icon>
                Notes
              </el-menu-item>
              <el-menu-item v-if="authStore.user?.role === 'admin'" index="/settings">
                <el-icon><Setting /></el-icon>
                Settings
              </el-menu-item>
            </el-menu>
          </div>
          <div class="user-menu">
            <el-dropdown>
              <span class="user-info">
                <el-icon><User /></el-icon>
                {{ authStore.user?.username }}
                <el-icon class="el-icon--right"><ArrowDown /></el-icon>
              </span>
              <template #dropdown>
                <el-dropdown-menu>
                  <el-dropdown-item @click="logout">
                    <el-icon><SwitchButton /></el-icon>
                    Logout
                  </el-dropdown-item>
                </el-dropdown-menu>
              </template>
            </el-dropdown>
          </div>
        </div>
      </el-header>

      <!-- Main Content -->
      <el-main class="app-main">
        <router-view />
      </el-main>
    </el-container>
  </div>
</template>

<script setup>
import { onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { useAuthStore } from './stores/auth'
import { ElMessage } from 'element-plus'

const router = useRouter()
const authStore = useAuthStore()

onMounted(() => {
  // Initialize auth state from localStorage
  authStore.initializeAuth()
})

const logout = async () => {
  try {
    await authStore.logout()
    ElMessage.success('Logged out successfully')
    router.push('/login')
  } catch (error) {
    ElMessage.error('Logout failed')
  }
}
</script>

<style scoped>
.app-container {
  min-height: 100vh;
}

.app-header {
  background-color: #fff;
  border-bottom: 1px solid #e4e7ed;
  padding: 0;
}

.header-content {
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: 100%;
  padding: 0 20px;
}

.logo h2 {
  margin: 0;
  color: #409eff;
  font-weight: 600;
}

.nav-menu {
  flex: 1;
  margin: 0 40px;
}

.nav-menu-items {
  border-bottom: none;
}

.user-menu {
  display: flex;
  align-items: center;
}

.user-info {
  display: flex;
  align-items: center;
  cursor: pointer;
  padding: 8px 12px;
  border-radius: 4px;
  transition: background-color 0.3s;
}

.user-info:hover {
  background-color: #f5f7fa;
}

.app-main {
  background-color: #f5f7fa;
  min-height: calc(100vh - 60px);
  padding: 20px;
}

/* Global styles */
:global(body) {
  margin: 0;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen',
    'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue',
    sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

:global(#app) {
  height: 100vh;
}
</style>