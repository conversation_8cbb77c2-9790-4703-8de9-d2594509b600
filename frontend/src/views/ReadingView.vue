<template>
  <div class="reading-container">
    <div v-if="exerciseStore.loading" class="loading-container">
      <el-skeleton :rows="8" animated />
    </div>
    
    <div v-else-if="exercise" class="reading-content">
      <!-- Header -->
      <div class="reading-header">
        <h1>Reading Comprehension Exercise</h1>
        <div class="exercise-meta">
          <el-tag :type="getDifficultyType(exercise.difficulty)" size="large">
            {{ exercise.difficulty }}
          </el-tag>
          <span class="word-count">{{ exercise.word_count }} words</span>
          <span class="timer" v-if="startTime">
            <el-icon><Timer /></el-icon>
            {{ formatTime(elapsedTime) }}
          </span>
        </div>
      </div>
      
      <!-- Article Display -->
      <el-card class="article-card">
        <template #header>
          <div class="article-header">
            <h2>{{ exercise.topic || 'Reading Article' }}</h2>
            <div class="reading-tools">
              <el-tooltip content="Select text to translate or get explanations">
                <el-icon><InfoFilled /></el-icon>
              </el-tooltip>
            </div>
          </div>
        </template>
        
        <div
          ref="articleRef"
          class="article-content"
          @mouseup="handleTextSelection"
        >
          {{ exercise.article }}
        </div>
      </el-card>
      
      <!-- Questions -->
      <el-card class="questions-card">
        <template #header>
          <h2>Comprehension Questions</h2>
        </template>
        
        <div class="questions-content">
          <div
            v-for="(question, index) in exercise.questions"
            :key="index"
            class="question-item"
          >
            <h3 class="question-title">
              {{ index + 1 }}. {{ question.question }}
            </h3>
            
            <el-radio-group
              v-model="answers[index]"
              class="question-options"
            >
              <el-radio
                v-for="option in question.options"
                :key="option"
                :label="option.charAt(0)"
                class="option-radio"
              >
                {{ option }}
              </el-radio>
            </el-radio-group>
          </div>
          
          <div class="submit-section">
            <el-button
              type="primary"
              size="large"
              :loading="submitting"
              @click="submitAnswers"
              :disabled="!allAnswered"
            >
              Submit Answers
            </el-button>
            <p class="submit-help">
              Answer all questions to submit your exercise
            </p>
          </div>
        </div>
      </el-card>
    </div>
    
    <!-- Context Menu -->
    <div
      v-if="showContextMenu"
      ref="contextMenuRef"
      class="context-menu"
      :style="contextMenuStyle"
    >
      <div class="context-menu-item" @click="translateSelection">
        <el-icon><Refresh /></el-icon>
        Translate
      </div>
      <div class="context-menu-item" @click="askAI">
        <el-icon><Message /></el-icon>
        Ask AI
      </div>
      <div class="context-menu-item" @click="makeNote">
        <el-icon><Edit /></el-icon>
        Make Note
      </div>
    </div>
    
    <!-- Translation Dialog -->
    <el-dialog
      v-model="showTranslationDialog"
      title="Translation"
      width="500px"
    >
      <div class="dialog-content">
        <div class="original-text">
          <strong>Original:</strong>
          <p>{{ selectedText }}</p>
        </div>
        <div class="translated-text" v-if="translationResult">
          <strong>Translation:</strong>
          <p>{{ translationResult.translated_text }}</p>
        </div>
        <div v-if="translating" class="loading">
          <el-icon class="is-loading"><Loading /></el-icon>
          Translating...
        </div>
      </div>
      <template #footer>
        <el-button @click="showTranslationDialog = false">Close</el-button>
        <el-button
          type="primary"
          @click="saveTranslationAsNote"
          :disabled="!translationResult"
        >
          Save to Notes
        </el-button>
      </template>
    </el-dialog>
    
    <!-- AI Explanation Dialog -->
    <el-dialog
      v-model="showAIDialog"
      title="AI Explanation"
      width="600px"
    >
      <div class="dialog-content">
        <div class="original-text">
          <strong>Selected Text:</strong>
          <p>{{ selectedText }}</p>
        </div>
        <div class="ai-explanation" v-if="aiResult">
          <strong>Explanation:</strong>
          <div class="explanation-content" v-html="formatExplanation(aiResult.answer)"></div>
        </div>
        <div v-if="askingAI" class="loading">
          <el-icon class="is-loading"><Loading /></el-icon>
          Getting explanation...
        </div>
      </div>
      <template #footer>
        <el-button @click="showAIDialog = false">Close</el-button>
        <el-button
          type="primary"
          @click="saveAIAsNote"
          :disabled="!aiResult"
        >
          Save to Notes
        </el-button>
      </template>
    </el-dialog>
    
    <!-- Note Dialog -->
    <el-dialog
      v-model="showNoteDialog"
      title="Create Note"
      width="500px"
    >
      <div class="dialog-content">
        <div class="original-text">
          <strong>Selected Text:</strong>
          <p>{{ selectedText }}</p>
        </div>
        <el-form :model="noteForm" label-width="80px">
          <el-form-item label="Note:">
            <el-input
              v-model="noteForm.content"
              type="textarea"
              :rows="4"
              placeholder="Enter your note..."
            />
          </el-form-item>
        </el-form>
      </div>
      <template #footer>
        <el-button @click="showNoteDialog = false">Cancel</el-button>
        <el-button
          type="primary"
          @click="saveNote"
          :loading="savingNote"
          :disabled="!noteForm.content.trim()"
        >
          Save Note
        </el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, computed, onMounted, onUnmounted, nextTick } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { useExerciseStore } from '../stores/exercise'
import { useNotesStore } from '../stores/notes'
import { helperService } from '../services/helpers'
import { ElMessage } from 'element-plus'
import {
  Timer, InfoFilled, Refresh, Message, Edit, Loading
} from '@element-plus/icons-vue'

const route = useRoute()
const router = useRouter()
const exerciseStore = useExerciseStore()
const notesStore = useNotesStore()

// Refs
const articleRef = ref()
const contextMenuRef = ref()

// State
const exercise = ref(null)
const answers = ref([])
const submitting = ref(false)
const startTime = ref(null)
const elapsedTime = ref(0)
const timer = ref(null)

// Context menu state
const showContextMenu = ref(false)
const selectedText = ref('')
const contextMenuStyle = ref({})

// Dialog states
const showTranslationDialog = ref(false)
const showAIDialog = ref(false)
const showNoteDialog = ref(false)

// Service states
const translating = ref(false)
const askingAI = ref(false)
const savingNote = ref(false)

// Results
const translationResult = ref(null)
const aiResult = ref(null)

// Note form
const noteForm = reactive({
  content: ''
})

// Computed
const allAnswered = computed(() => {
  return answers.value.length === exercise.value?.questions.length &&
         answers.value.every(answer => answer !== undefined && answer !== '')
})

// Lifecycle
onMounted(async () => {
  await loadExercise()
  startTimer()
  document.addEventListener('click', hideContextMenu)
})

onUnmounted(() => {
  stopTimer()
  document.removeEventListener('click', hideContextMenu)
})

// Methods
const loadExercise = async () => {
  try {
    const exerciseId = route.params.id
    const exerciseData = await exerciseStore.getExercise(exerciseId)
    exercise.value = exerciseData
    answers.value = new Array(exerciseData.questions.length).fill('')
  } catch (error) {
    ElMessage.error('Failed to load exercise')
    router.push('/dashboard')
  }
}

const startTimer = () => {
  startTime.value = Date.now()
  timer.value = setInterval(() => {
    elapsedTime.value = Math.floor((Date.now() - startTime.value) / 1000)
  }, 1000)
}

const stopTimer = () => {
  if (timer.value) {
    clearInterval(timer.value)
    timer.value = null
  }
}

const formatTime = (seconds) => {
  const mins = Math.floor(seconds / 60)
  const secs = seconds % 60
  return `${mins}:${secs.toString().padStart(2, '0')}`
}

const handleTextSelection = (event) => {
  const selection = window.getSelection()
  const text = selection.toString().trim()
  
  if (text.length > 0) {
    selectedText.value = text
    showContextMenu.value = true
    
    // Position context menu
    const rect = selection.getRangeAt(0).getBoundingClientRect()
    contextMenuStyle.value = {
      position: 'fixed',
      top: `${rect.bottom + 5}px`,
      left: `${rect.left}px`,
      zIndex: 9999
    }
  }
}

const hideContextMenu = (event) => {
  if (contextMenuRef.value && !contextMenuRef.value.contains(event.target)) {
    showContextMenu.value = false
  }
}

const translateSelection = async () => {
  showContextMenu.value = false
  showTranslationDialog.value = true
  translating.value = true
  translationResult.value = null
  
  try {
    const result = await helperService.translateText(selectedText.value)
    translationResult.value = result
  } catch (error) {
    ElMessage.error('Translation failed')
  } finally {
    translating.value = false
  }
}

const askAI = async () => {
  showContextMenu.value = false
  showAIDialog.value = true
  askingAI.value = true
  aiResult.value = null
  
  try {
    const result = await helperService.askAI(selectedText.value, exercise.value.article)
    aiResult.value = result
  } catch (error) {
    ElMessage.error('AI explanation failed')
  } finally {
    askingAI.value = false
  }
}

const makeNote = () => {
  showContextMenu.value = false
  noteForm.content = ''
  showNoteDialog.value = true
}

const saveTranslationAsNote = async () => {
  if (!translationResult.value) return
  
  const noteContent = `Translation: ${translationResult.value.translated_text}`
  await saveNoteWithContent(noteContent)
  showTranslationDialog.value = false
}

const saveAIAsNote = async () => {
  if (!aiResult.value) return
  
  const noteContent = `AI Explanation: ${aiResult.value.answer}`
  await saveNoteWithContent(noteContent)
  showAIDialog.value = false
}

const saveNote = async () => {
  await saveNoteWithContent(noteForm.content)
  showNoteDialog.value = false
}

const saveNoteWithContent = async (content) => {
  savingNote.value = true
  try {
    await notesStore.createNote({
      selected_text: selectedText.value,
      note_content: content,
      exercise_id: exercise.value.id
    })
    ElMessage.success('Note saved successfully')
  } catch (error) {
    ElMessage.error('Failed to save note')
  } finally {
    savingNote.value = false
  }
}

const submitAnswers = async () => {
  if (!allAnswered.value) return
  
  submitting.value = true
  try {
    stopTimer()
    const result = await exerciseStore.submitAnswers(exercise.value.id, answers.value)
    ElMessage.success('Answers submitted successfully!')
    router.push(`/result/${exercise.value.id}`)
  } catch (error) {
    ElMessage.error('Failed to submit answers')
    startTimer() // Restart timer if submission failed
  } finally {
    submitting.value = false
  }
}

const getDifficultyType = (difficulty) => {
  const types = {
    'Beginner': 'success',
    'Intermediate': 'warning',
    'Advanced': 'danger'
  }
  return types[difficulty] || 'info'
}

const formatExplanation = (text) => {
  return text.replace(/\n/g, '<br>')
}
</script>

<style scoped>
.reading-container {
  max-width: 1000px;
  margin: 0 auto;
}

.loading-container {
  padding: 40px;
}

.reading-header {
  text-align: center;
  margin-bottom: 30px;
}

.reading-header h1 {
  margin: 0 0 16px 0;
  color: #303133;
  font-size: 28px;
  font-weight: 600;
}

.exercise-meta {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 20px;
  flex-wrap: wrap;
}

.word-count, .timer {
  display: flex;
  align-items: center;
  gap: 4px;
  color: #606266;
  font-size: 14px;
}

.article-card {
  margin-bottom: 30px;
}

.article-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.article-header h2 {
  margin: 0;
  color: #303133;
  font-size: 20px;
}

.article-content {
  font-size: 16px;
  line-height: 1.8;
  color: #303133;
  text-align: justify;
  user-select: text;
  cursor: text;
  padding: 20px 0;
}

.questions-card {
  margin-bottom: 30px;
}

.questions-content {
  padding: 20px 0;
}

.question-item {
  margin-bottom: 30px;
  padding-bottom: 20px;
  border-bottom: 1px solid #ebeef5;
}

.question-item:last-child {
  border-bottom: none;
  margin-bottom: 0;
}

.question-title {
  margin: 0 0 16px 0;
  color: #303133;
  font-size: 16px;
  font-weight: 600;
  line-height: 1.5;
}

.question-options {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.option-radio {
  margin: 0;
  padding: 12px;
  border: 1px solid #dcdfe6;
  border-radius: 8px;
  transition: all 0.2s;
}

.option-radio:hover {
  border-color: #409eff;
  background-color: #f8faff;
}

.option-radio.is-checked {
  border-color: #409eff;
  background-color: #f0f7ff;
}

.submit-section {
  text-align: center;
  margin-top: 40px;
  padding-top: 30px;
  border-top: 2px solid #ebeef5;
}

.submit-help {
  margin: 12px 0 0 0;
  color: #909399;
  font-size: 14px;
}

/* Context Menu */
.context-menu {
  background: white;
  border: 1px solid #dcdfe6;
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  padding: 8px 0;
  min-width: 120px;
}

.context-menu-item {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px 16px;
  cursor: pointer;
  transition: background-color 0.2s;
  font-size: 14px;
  color: #303133;
}

.context-menu-item:hover {
  background-color: #f5f7fa;
}

/* Dialog Content */
.dialog-content {
  padding: 10px 0;
}

.original-text {
  margin-bottom: 20px;
}

.original-text strong,
.translated-text strong,
.ai-explanation strong {
  color: #303133;
  font-size: 14px;
}

.original-text p,
.translated-text p {
  margin: 8px 0 0 0;
  padding: 12px;
  background-color: #f5f7fa;
  border-radius: 6px;
  color: #606266;
  line-height: 1.6;
}

.explanation-content {
  margin: 8px 0 0 0;
  padding: 12px;
  background-color: #f5f7fa;
  border-radius: 6px;
  color: #606266;
  line-height: 1.6;
}

.loading {
  display: flex;
  align-items: center;
  gap: 8px;
  color: #409eff;
  font-size: 14px;
  margin: 20px 0;
}

/* Responsive */
@media (max-width: 768px) {
  .reading-container {
    padding: 0 10px;
  }
  
  .exercise-meta {
    flex-direction: column;
    gap: 10px;
  }
  
  .article-content {
    font-size: 15px;
    line-height: 1.7;
  }
  
  .question-options {
    gap: 8px;
  }
  
  .option-radio {
    padding: 10px;
  }
}
</style>