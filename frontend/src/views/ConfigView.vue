<template>
  <div class="config-container">
    <el-card class="config-card">
      <template #header>
        <div class="card-header">
          <h2>Create New Reading Exercise</h2>
          <p>Configure your personalized reading comprehension exercise</p>
        </div>
      </template>
      
      <el-form
        ref="configFormRef"
        :model="configForm"
        :rules="rules"
        label-width="140px"
        class="config-form"
      >
        <el-form-item label="Topic" prop="topic">
          <el-input
            v-model="configForm.topic"
            placeholder="e.g., Technology, Environment, History, Science..."
            size="large"
          />
          <div class="form-help">
            Enter a topic of interest or leave blank for a general article
          </div>
        </el-form-item>
        
        <el-form-item label="Difficulty Level" prop="difficulty">
          <el-select
            v-model="configForm.difficulty"
            placeholder="Select difficulty level"
            size="large"
            style="width: 100%"
          >
            <el-option
              label="Beginner"
              value="Beginner"
            >
              <div class="option-content">
                <span>Beginner</span>
                <small>Simple vocabulary, basic concepts</small>
              </div>
            </el-option>
            <el-option
              label="Intermediate"
              value="Intermediate"
            >
              <div class="option-content">
                <span>Intermediate</span>
                <small>Moderate vocabulary, complex ideas</small>
              </div>
            </el-option>
            <el-option
              label="Advanced"
              value="Advanced"
            >
              <div class="option-content">
                <span>Advanced</span>
                <small>Rich vocabulary, sophisticated concepts</small>
              </div>
            </el-option>
          </el-select>
        </el-form-item>
        
        <el-form-item label="Word Count" prop="word_count">
          <el-slider
            v-model="configForm.word_count"
            :min="100"
            :max="1000"
            :step="50"
            show-stops
            show-input
            :format-tooltip="formatWordCount"
          />
          <div class="form-help">
            Recommended: 200-400 words for optimal reading practice
          </div>
        </el-form-item>
        
        <el-form-item label="Questions" prop="question_count">
          <el-input-number
            v-model="configForm.question_count"
            :min="3"
            :max="10"
            size="large"
            style="width: 100%"
          />
          <div class="form-help">
            Number of comprehension questions (3-10)
          </div>
        </el-form-item>
        
        <el-form-item label="AI Model" prop="model_id">
          <el-select
            v-model="configForm.model_id"
            placeholder="Select AI model (optional)"
            size="large"
            style="width: 100%"
            clearable
          >
            <el-option
              v-for="model in availableModels"
              :key="model.id"
              :label="model.display_name"
              :value="model.id"
            >
              <div class="option-content">
                <span>{{ model.display_name }}</span>
                <small v-if="model.is_default_generator">(Default)</small>
              </div>
            </el-option>
          </el-select>
          <div class="form-help">
            Leave empty to use the default model
          </div>
        </el-form-item>
        
        <el-form-item>
          <el-button
            type="primary"
            size="large"
            :loading="exerciseStore.loading"
            @click="generateExercise"
            class="generate-button"
          >
            <el-icon><Star /></el-icon>
            Generate Exercise
          </el-button>
          <el-button
            size="large"
            @click="resetForm"
          >
            Reset
          </el-button>
        </el-form-item>
      </el-form>
    </el-card>
    
    <!-- Preview Card -->
    <el-card v-if="showPreview" class="preview-card">
      <template #header>
        <h3>Exercise Preview</h3>
      </template>
      <div class="preview-content">
        <div class="preview-item">
          <strong>Topic:</strong> {{ configForm.topic || 'General' }}
        </div>
        <div class="preview-item">
          <strong>Difficulty:</strong> 
          <el-tag :type="getDifficultyType(configForm.difficulty)" size="small">
            {{ configForm.difficulty }}
          </el-tag>
        </div>
        <div class="preview-item">
          <strong>Word Count:</strong> ~{{ configForm.word_count }} words
        </div>
        <div class="preview-item">
          <strong>Questions:</strong> {{ configForm.question_count }} questions
        </div>
        <div class="preview-item">
          <strong>Estimated Time:</strong> {{ estimatedTime }} minutes
        </div>
      </div>
    </el-card>
  </div>
</template>

<script setup>
import { ref, reactive, computed, onMounted, watch } from 'vue'
import { useRouter } from 'vue-router'
import { useExerciseStore } from '../stores/exercise'
import { useSettingsStore } from '../stores/settings'
import { ElMessage } from 'element-plus'
import { Star } from '@element-plus/icons-vue'

const router = useRouter()
const exerciseStore = useExerciseStore()
const settingsStore = useSettingsStore()

const configFormRef = ref()
const availableModels = ref([])

const configForm = reactive({
  topic: '',
  difficulty: 'Intermediate',
  word_count: 300,
  question_count: 5,
  model_id: null
})

const rules = {
  difficulty: [
    { required: true, message: 'Please select difficulty level', trigger: 'change' }
  ],
  word_count: [
    { required: true, message: 'Please set word count', trigger: 'blur' }
  ],
  question_count: [
    { required: true, message: 'Please set question count', trigger: 'blur' }
  ]
}

const showPreview = computed(() => {
  return configForm.difficulty && configForm.word_count && configForm.question_count
})

const estimatedTime = computed(() => {
  // Rough estimate: 200 words per minute reading + 1 minute per question
  const readingTime = configForm.word_count / 200
  const questionTime = configForm.question_count * 1
  return Math.ceil(readingTime + questionTime)
})

onMounted(async () => {
  await loadModels()
})

const loadModels = async () => {
  try {
    await settingsStore.getModels()
    availableModels.value = settingsStore.models
  } catch (error) {
    console.error('Failed to load models:', error)
  }
}

const generateExercise = async () => {
  if (!configFormRef.value) return
  
  await configFormRef.value.validate(async (valid) => {
    if (valid) {
      try {
        const exercise = await exerciseStore.generateExercise(configForm)
        ElMessage.success('Exercise generated successfully!')
        router.push(`/reading/${exercise.id}`)
      } catch (error) {
        ElMessage.error('Failed to generate exercise. Please try again.')
        console.error('Generation error:', error)
      }
    }
  })
}

const resetForm = () => {
  configFormRef.value?.resetFields()
  Object.assign(configForm, {
    topic: '',
    difficulty: 'Intermediate',
    word_count: 300,
    question_count: 5,
    model_id: null
  })
}

const formatWordCount = (value) => {
  return `${value} words`
}

const getDifficultyType = (difficulty) => {
  const types = {
    'Beginner': 'success',
    'Intermediate': 'warning',
    'Advanced': 'danger'
  }
  return types[difficulty] || 'info'
}
</script>

<style scoped>
.config-container {
  max-width: 800px;
  margin: 0 auto;
}

.config-card {
  margin-bottom: 20px;
}

.card-header {
  text-align: center;
}

.card-header h2 {
  margin: 0 0 8px 0;
  color: #303133;
  font-size: 24px;
  font-weight: 600;
}

.card-header p {
  margin: 0;
  color: #606266;
  font-size: 14px;
}

.config-form {
  padding: 20px 0;
}

.form-help {
  margin-top: 4px;
  font-size: 12px;
  color: #909399;
  line-height: 1.4;
}

.option-content {
  display: flex;
  flex-direction: column;
}

.option-content small {
  color: #909399;
  font-size: 12px;
}

.generate-button {
  width: 200px;
  height: 44px;
  font-size: 16px;
  font-weight: 500;
  margin-right: 12px;
}

.preview-card {
  background: linear-gradient(135deg, #f8faff 0%, #f0f7ff 100%);
  border: 1px solid #d9ecff;
}

.preview-content {
  padding: 10px 0;
}

.preview-item {
  display: flex;
  align-items: center;
  margin-bottom: 12px;
  font-size: 14px;
}

.preview-item:last-child {
  margin-bottom: 0;
}

.preview-item strong {
  min-width: 120px;
  color: #303133;
  margin-right: 12px;
}

/* Responsive design */
@media (max-width: 768px) {
  .config-container {
    padding: 0 10px;
  }
  
  .config-form {
    padding: 10px 0;
  }
  
  .generate-button {
    width: 100%;
    margin-bottom: 12px;
    margin-right: 0;
  }
}
</style>