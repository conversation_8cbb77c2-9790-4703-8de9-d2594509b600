<template>
  <div class="settings-container">
    <div class="settings-header">
      <h1>System Settings</h1>
      <p>Configure AI providers and models for the platform</p>
    </div>
    
    <!-- Providers Section -->
    <el-card class="section-card">
      <template #header>
        <div class="section-header">
          <h2>AI Providers</h2>
          <el-button type="primary" @click="showProviderDialog = true">
            <el-icon><Plus /></el-icon>
            Add Provider
          </el-button>
        </div>
      </template>
      
      <div v-if="settingsStore.loading" class="loading">
        <el-skeleton :rows="3" animated />
      </div>
      
      <div v-else>
        <el-table :data="settingsStore.providers" style="width: 100%">
          <el-table-column prop="name" label="Name" />
          <el-table-column prop="api_base_url" label="API Base URL" />
          <el-table-column label="API Key">
            <template #default="scope">
              <span v-if="scope.row.api_key">
                {{ maskApiKey(scope.row.api_key) }}
              </span>
              <span v-else class="no-key">No API Key</span>
            </template>
          </el-table-column>
          <el-table-column label="Actions" width="150">
            <template #default="scope">
              <el-button
                type="text"
                size="small"
                @click="editProvider(scope.row)"
              >
                Edit
              </el-button>
              <el-button
                type="text"
                size="small"
                @click="deleteProvider(scope.row.id)"
                class="delete-btn"
              >
                Delete
              </el-button>
            </template>
          </el-table-column>
        </el-table>
      </div>
    </el-card>
    
    <!-- Models Section -->
    <el-card class="section-card">
      <template #header>
        <div class="section-header">
          <h2>AI Models</h2>
          <el-button type="primary" @click="showModelDialog = true">
            <el-icon><Plus /></el-icon>
            Add Model
          </el-button>
        </div>
      </template>
      
      <div v-if="settingsStore.loading" class="loading">
        <el-skeleton :rows="3" animated />
      </div>
      
      <div v-else>
        <el-table :data="settingsStore.models" style="width: 100%">
          <el-table-column prop="display_name" label="Display Name" />
          <el-table-column prop="model_name" label="Model Name" />
          <el-table-column label="Provider">
            <template #default="scope">
              {{ scope.row.provider?.name }}
            </template>
          </el-table-column>
          <el-table-column label="Default Generator" width="140">
            <template #default="scope">
              <el-tag v-if="scope.row.is_default_generator" type="success" size="small">
                Yes
              </el-tag>
              <span v-else>-</span>
            </template>
          </el-table-column>
          <el-table-column label="Default Judge" width="120">
            <template #default="scope">
              <el-tag v-if="scope.row.is_default_judge" type="success" size="small">
                Yes
              </el-tag>
              <span v-else>-</span>
            </template>
          </el-table-column>
          <el-table-column label="Actions" width="150">
            <template #default="scope">
              <el-button
                type="text"
                size="small"
                @click="editModel(scope.row)"
              >
                Edit
              </el-button>
              <el-button
                type="text"
                size="small"
                @click="deleteModel(scope.row.id)"
                class="delete-btn"
              >
                Delete
              </el-button>
            </template>
          </el-table-column>
        </el-table>
      </div>
    </el-card>
    
    <!-- Default Models Section -->
    <el-card class="section-card">
      <template #header>
        <h2>Default Models</h2>
      </template>
      
      <el-form :model="defaultsForm" label-width="180px" class="defaults-form">
        <el-form-item label="Default Generator:">
          <el-select
            v-model="defaultsForm.default_generator_id"
            placeholder="Select default generator model"
            style="width: 100%"
          >
            <el-option
              v-for="model in settingsStore.models"
              :key="model.id"
              :label="model.display_name"
              :value="model.id"
            />
          </el-select>
        </el-form-item>
        
        <el-form-item label="Default Judge:">
          <el-select
            v-model="defaultsForm.default_judge_id"
            placeholder="Select default judge model"
            style="width: 100%"
          >
            <el-option
              v-for="model in settingsStore.models"
              :key="model.id"
              :label="model.display_name"
              :value="model.id"
            />
          </el-select>
        </el-form-item>
        
        <el-form-item>
          <el-button
            type="primary"
            @click="saveDefaults"
            :loading="settingsStore.loading"
          >
            Save Defaults
          </el-button>
        </el-form-item>
      </el-form>
    </el-card>
    
    <!-- Provider Dialog -->
    <el-dialog
      v-model="showProviderDialog"
      :title="editingProvider ? 'Edit Provider' : 'Add Provider'"
      width="600px"
    >
      <el-form
        ref="providerFormRef"
        :model="providerForm"
        :rules="providerRules"
        label-width="120px"
      >
        <el-form-item label="Name:" prop="name">
          <el-input v-model="providerForm.name" placeholder="e.g., OpenRouter" />
        </el-form-item>
        
        <el-form-item label="API Base URL:" prop="api_base_url">
          <el-input
            v-model="providerForm.api_base_url"
            placeholder="e.g., https://openrouter.ai/api/v1"
          />
        </el-form-item>
        
        <el-form-item label="API Key:">
          <el-input
            v-model="providerForm.api_key"
            type="password"
            placeholder="Optional API key"
            show-password
          />
        </el-form-item>
      </el-form>
      
      <template #footer>
        <el-button @click="showProviderDialog = false">Cancel</el-button>
        <el-button
          type="primary"
          @click="saveProvider"
          :loading="settingsStore.loading"
        >
          {{ editingProvider ? 'Update' : 'Create' }}
        </el-button>
      </template>
    </el-dialog>
    
    <!-- Model Dialog -->
    <el-dialog
      v-model="showModelDialog"
      :title="editingModel ? 'Edit Model' : 'Add Model'"
      width="600px"
    >
      <el-form
        ref="modelFormRef"
        :model="modelForm"
        :rules="modelRules"
        label-width="140px"
      >
        <el-form-item label="Provider:" prop="provider_id">
          <el-select
            v-model="modelForm.provider_id"
            placeholder="Select provider"
            style="width: 100%"
          >
            <el-option
              v-for="provider in settingsStore.providers"
              :key="provider.id"
              :label="provider.name"
              :value="provider.id"
            />
          </el-select>
        </el-form-item>
        
        <el-form-item label="Model Name:" prop="model_name">
          <el-input
            v-model="modelForm.model_name"
            placeholder="e.g., openai/gpt-4o"
          />
        </el-form-item>
        
        <el-form-item label="Display Name:" prop="display_name">
          <el-input
            v-model="modelForm.display_name"
            placeholder="e.g., GPT-4o (OpenRouter)"
          />
        </el-form-item>
        
        <el-form-item label="Default Generator:">
          <el-switch v-model="modelForm.is_default_generator" />
        </el-form-item>
        
        <el-form-item label="Default Judge:">
          <el-switch v-model="modelForm.is_default_judge" />
        </el-form-item>
      </el-form>
      
      <template #footer>
        <el-button @click="showModelDialog = false">Cancel</el-button>
        <el-button
          type="primary"
          @click="saveModel"
          :loading="settingsStore.loading"
        >
          {{ editingModel ? 'Update' : 'Create' }}
        </el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted, computed } from 'vue'
import { useSettingsStore } from '../stores/settings'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Plus } from '@element-plus/icons-vue'

const settingsStore = useSettingsStore()

// Dialog states
const showProviderDialog = ref(false)
const showModelDialog = ref(false)
const editingProvider = ref(null)
const editingModel = ref(null)

// Form refs
const providerFormRef = ref()
const modelFormRef = ref()

// Forms
const providerForm = reactive({
  name: '',
  api_base_url: '',
  api_key: ''
})

const modelForm = reactive({
  provider_id: null,
  model_name: '',
  display_name: '',
  is_default_generator: false,
  is_default_judge: false
})

const defaultsForm = reactive({
  default_generator_id: null,
  default_judge_id: null
})

// Rules
const providerRules = {
  name: [
    { required: true, message: 'Please input provider name', trigger: 'blur' }
  ],
  api_base_url: [
    { required: true, message: 'Please input API base URL', trigger: 'blur' }
  ]
}

const modelRules = {
  provider_id: [
    { required: true, message: 'Please select provider', trigger: 'change' }
  ],
  model_name: [
    { required: true, message: 'Please input model name', trigger: 'blur' }
  ],
  display_name: [
    { required: true, message: 'Please input display name', trigger: 'blur' }
  ]
}

// Lifecycle
onMounted(async () => {
  await loadData()
  initializeDefaults()
})

// Methods
const loadData = async () => {
  try {
    await Promise.all([
      settingsStore.getProviders(),
      settingsStore.getModels()
    ])
  } catch (error) {
    ElMessage.error('Failed to load settings data')
  }
}

const initializeDefaults = () => {
  const defaultGenerator = settingsStore.models.find(m => m.is_default_generator)
  const defaultJudge = settingsStore.models.find(m => m.is_default_judge)
  
  if (defaultGenerator) {
    defaultsForm.default_generator_id = defaultGenerator.id
  }
  if (defaultJudge) {
    defaultsForm.default_judge_id = defaultJudge.id
  }
}

const editProvider = (provider) => {
  editingProvider.value = provider
  Object.assign(providerForm, {
    name: provider.name,
    api_base_url: provider.api_base_url,
    api_key: provider.api_key || ''
  })
  showProviderDialog.value = true
}

const saveProvider = async () => {
  if (!providerFormRef.value) return
  
  await providerFormRef.value.validate(async (valid) => {
    if (valid) {
      try {
        if (editingProvider.value) {
          await settingsStore.updateProvider(editingProvider.value.id, providerForm)
          ElMessage.success('Provider updated successfully')
        } else {
          await settingsStore.createProvider(providerForm)
          ElMessage.success('Provider created successfully')
        }
        showProviderDialog.value = false
        resetProviderForm()
      } catch (error) {
        ElMessage.error('Failed to save provider')
      }
    }
  })
}

const deleteProvider = async (providerId) => {
  try {
    await ElMessageBox.confirm(
      'Are you sure you want to delete this provider?',
      'Confirm Delete',
      {
        confirmButtonText: 'Delete',
        cancelButtonText: 'Cancel',
        type: 'warning',
      }
    )
    
    await settingsStore.deleteProvider(providerId)
    ElMessage.success('Provider deleted successfully')
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('Failed to delete provider')
    }
  }
}

const editModel = (model) => {
  editingModel.value = model
  Object.assign(modelForm, {
    provider_id: model.provider_id,
    model_name: model.model_name,
    display_name: model.display_name,
    is_default_generator: model.is_default_generator,
    is_default_judge: model.is_default_judge
  })
  showModelDialog.value = true
}

const saveModel = async () => {
  if (!modelFormRef.value) return
  
  await modelFormRef.value.validate(async (valid) => {
    if (valid) {
      try {
        if (editingModel.value) {
          await settingsStore.updateModel(editingModel.value.id, modelForm)
          ElMessage.success('Model updated successfully')
        } else {
          await settingsStore.createModel(modelForm)
          ElMessage.success('Model created successfully')
        }
        showModelDialog.value = false
        resetModelForm()
        initializeDefaults() // Refresh defaults
      } catch (error) {
        ElMessage.error('Failed to save model')
      }
    }
  })
}

const deleteModel = async (modelId) => {
  try {
    await ElMessageBox.confirm(
      'Are you sure you want to delete this model?',
      'Confirm Delete',
      {
        confirmButtonText: 'Delete',
        cancelButtonText: 'Cancel',
        type: 'warning',
      }
    )
    
    await settingsStore.deleteModel(modelId)
    ElMessage.success('Model deleted successfully')
    initializeDefaults() // Refresh defaults
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('Failed to delete model')
    }
  }
}

const saveDefaults = async () => {
  try {
    await settingsStore.setDefaults(defaultsForm)
    ElMessage.success('Default models updated successfully')
  } catch (error) {
    ElMessage.error('Failed to update defaults')
  }
}

const resetProviderForm = () => {
  editingProvider.value = null
  Object.assign(providerForm, {
    name: '',
    api_base_url: '',
    api_key: ''
  })
}

const resetModelForm = () => {
  editingModel.value = null
  Object.assign(modelForm, {
    provider_id: null,
    model_name: '',
    display_name: '',
    is_default_generator: false,
    is_default_judge: false
  })
}

const maskApiKey = (apiKey) => {
  if (!apiKey) return ''
  if (apiKey.length <= 8) return '*'.repeat(apiKey.length)
  return apiKey.substring(0, 4) + '*'.repeat(apiKey.length - 8) + apiKey.substring(apiKey.length - 4)
}
</script>

<style scoped>
.settings-container {
  max-width: 1200px;
  margin: 0 auto;
}

.settings-header {
  text-align: center;
  margin-bottom: 40px;
}

.settings-header h1 {
  margin: 0 0 8px 0;
  color: #303133;
  font-size: 28px;
  font-weight: 600;
}

.settings-header p {
  margin: 0;
  color: #606266;
  font-size: 16px;
}

.section-card {
  margin-bottom: 30px;
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.section-header h2 {
  margin: 0;
  color: #303133;
  font-size: 20px;
  font-weight: 600;
}

.loading {
  padding: 20px;
}

.no-key {
  color: #909399;
  font-style: italic;
}

.delete-btn {
  color: #f56c6c;
}

.delete-btn:hover {
  color: #f56c6c;
}

.defaults-form {
  max-width: 500px;
  padding: 20px 0;
}

/* Responsive */
@media (max-width: 768px) {
  .settings-container {
    padding: 0 10px;
  }
  
  .section-header {
    flex-direction: column;
    gap: 16px;
    align-items: stretch;
  }
  
  :deep(.el-table) {
    font-size: 12px;
  }
  
  :deep(.el-table .cell) {
    padding: 8px 4px;
  }
}
</style>