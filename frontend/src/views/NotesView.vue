<template>
  <div class="notes-container">
    <div class="notes-header">
      <h1>My Notes</h1>
      <p>Review your saved notes, translations, and AI explanations</p>
    </div>
    
    <!-- Search and Filters -->
    <el-card class="search-card">
      <div class="search-controls">
        <el-input
          v-model="searchQuery"
          placeholder="Search notes..."
          size="large"
          clearable
          @input="handleSearch"
          class="search-input"
        >
          <template #prefix>
            <el-icon><Search /></el-icon>
          </template>
        </el-input>
        
        <el-button
          type="primary"
          size="large"
          @click="searchNotes"
          :loading="notesStore.loading"
        >
          Search
        </el-button>
        
        <el-button
          size="large"
          @click="clearSearch"
        >
          Clear
        </el-button>
      </div>
    </el-card>
    
    <!-- Notes List -->
    <div v-if="notesStore.loading" class="loading-container">
      <el-skeleton :rows="4" animated />
    </div>
    
    <div v-else-if="notesStore.notes.length === 0" class="empty-state">
      <el-empty description="No notes found">
        <p>Start reading exercises and create notes by selecting text!</p>
        <el-button type="primary" @click="$router.push('/config')">
          Create Exercise
        </el-button>
      </el-empty>
    </div>
    
    <div v-else class="notes-list">
      <el-card
        v-for="note in notesStore.notes"
        :key="note.id"
        class="note-card"
        shadow="hover"
      >
        <div class="note-content">
          <div class="note-header">
            <div class="note-meta">
              <span class="note-date">
                {{ formatDate(note.created_at) }}
              </span>
              <el-tag v-if="note.exercise_id" size="small" type="info">
                Exercise #{{ note.exercise_id }}
              </el-tag>
            </div>
            <div class="note-actions">
              <el-button
                type="text"
                size="small"
                @click="editNote(note)"
              >
                <el-icon><Edit /></el-icon>
              </el-button>
              <el-button
                type="text"
                size="small"
                @click="deleteNote(note.id)"
                class="delete-btn"
              >
                <el-icon><Delete /></el-icon>
              </el-button>
            </div>
          </div>
          
          <div class="selected-text">
            <strong>Selected Text:</strong>
            <blockquote>{{ note.selected_text }}</blockquote>
          </div>
          
          <div class="note-text">
            <strong>Note:</strong>
            <p>{{ note.note_content }}</p>
          </div>
        </div>
      </el-card>
    </div>
    
    <!-- Edit Note Dialog -->
    <el-dialog
      v-model="showEditDialog"
      title="Edit Note"
      width="600px"
    >
      <div v-if="editingNote" class="dialog-content">
        <div class="selected-text">
          <strong>Selected Text:</strong>
          <blockquote>{{ editingNote.selected_text }}</blockquote>
        </div>
        
        <el-form :model="editForm" label-width="80px">
          <el-form-item label="Note:">
            <el-input
              v-model="editForm.content"
              type="textarea"
              :rows="6"
              placeholder="Enter your note..."
            />
          </el-form-item>
        </el-form>
      </div>
      
      <template #footer>
        <el-button @click="showEditDialog = false">Cancel</el-button>
        <el-button
          type="primary"
          @click="saveEdit"
          :loading="saving"
          :disabled="!editForm.content.trim()"
        >
          Save Changes
        </el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { useNotesStore } from '../stores/notes'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Search, Edit, Delete } from '@element-plus/icons-vue'

const router = useRouter()
const notesStore = useNotesStore()

// State
const searchQuery = ref('')
const showEditDialog = ref(false)
const editingNote = ref(null)
const saving = ref(false)

const editForm = reactive({
  content: ''
})

// Lifecycle
onMounted(() => {
  loadNotes()
})

// Methods
const loadNotes = async () => {
  try {
    await notesStore.getNotes()
  } catch (error) {
    ElMessage.error('Failed to load notes')
  }
}

const handleSearch = (value) => {
  if (!value.trim()) {
    loadNotes()
  }
}

const searchNotes = async () => {
  if (!searchQuery.value.trim()) {
    await loadNotes()
    return
  }
  
  try {
    await notesStore.searchNotes(searchQuery.value)
  } catch (error) {
    ElMessage.error('Search failed')
  }
}

const clearSearch = async () => {
  searchQuery.value = ''
  await loadNotes()
}

const editNote = (note) => {
  editingNote.value = note
  editForm.content = note.note_content
  showEditDialog.value = true
}

const saveEdit = async () => {
  if (!editForm.content.trim()) return
  
  saving.value = true
  try {
    await notesStore.updateNote(editingNote.value.id, {
      note_content: editForm.content
    })
    ElMessage.success('Note updated successfully')
    showEditDialog.value = false
  } catch (error) {
    ElMessage.error('Failed to update note')
  } finally {
    saving.value = false
  }
}

const deleteNote = async (noteId) => {
  try {
    await ElMessageBox.confirm(
      'Are you sure you want to delete this note?',
      'Confirm Delete',
      {
        confirmButtonText: 'Delete',
        cancelButtonText: 'Cancel',
        type: 'warning',
      }
    )
    
    await notesStore.deleteNote(noteId)
    ElMessage.success('Note deleted successfully')
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('Failed to delete note')
    }
  }
}

const formatDate = (dateString) => {
  return new Date(dateString).toLocaleString('en-US', {
    year: 'numeric',
    month: 'short',
    day: 'numeric',
    hour: '2-digit',
    minute: '2-digit'
  })
}
</script>

<style scoped>
.notes-container {
  max-width: 900px;
  margin: 0 auto;
}

.notes-header {
  text-align: center;
  margin-bottom: 30px;
}

.notes-header h1 {
  margin: 0 0 8px 0;
  color: #303133;
  font-size: 28px;
  font-weight: 600;
}

.notes-header p {
  margin: 0;
  color: #606266;
  font-size: 16px;
}

.search-card {
  margin-bottom: 30px;
}

.search-controls {
  display: flex;
  gap: 12px;
  align-items: center;
}

.search-input {
  flex: 1;
}

.loading-container {
  padding: 40px;
}

.empty-state {
  text-align: center;
  padding: 60px 20px;
}

.empty-state p {
  margin: 16px 0 24px 0;
  color: #606266;
  font-size: 16px;
}

.notes-list {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.note-card {
  transition: transform 0.2s;
}

.note-card:hover {
  transform: translateY(-2px);
}

.note-content {
  padding: 10px 0;
}

.note-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.note-meta {
  display: flex;
  align-items: center;
  gap: 12px;
}

.note-date {
  color: #909399;
  font-size: 14px;
}

.note-actions {
  display: flex;
  gap: 4px;
}

.delete-btn {
  color: #f56c6c;
}

.delete-btn:hover {
  color: #f56c6c;
  background-color: #fef0f0;
}

.selected-text {
  margin-bottom: 16px;
}

.selected-text strong,
.note-text strong {
  color: #303133;
  font-size: 14px;
  display: block;
  margin-bottom: 8px;
}

.selected-text blockquote {
  margin: 0;
  padding: 12px 16px;
  background-color: #f8faff;
  border-left: 4px solid #409eff;
  border-radius: 0 6px 6px 0;
  color: #606266;
  font-style: italic;
  line-height: 1.6;
}

.note-text p {
  margin: 0;
  padding: 12px;
  background-color: #f5f7fa;
  border-radius: 6px;
  color: #303133;
  line-height: 1.6;
  white-space: pre-wrap;
}

.dialog-content {
  padding: 10px 0;
}

/* Responsive */
@media (max-width: 768px) {
  .notes-container {
    padding: 0 10px;
  }
  
  .search-controls {
    flex-direction: column;
  }
  
  .search-input {
    width: 100%;
  }
  
  .note-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 12px;
  }
  
  .note-meta {
    flex-direction: column;
    align-items: flex-start;
    gap: 8px;
  }
}
</style>