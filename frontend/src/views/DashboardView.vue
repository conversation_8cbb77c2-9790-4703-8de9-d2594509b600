<template>
  <div class="dashboard">
    <div class="dashboard-header">
      <h1>Welcome back, {{ authStore.user?.username }}!</h1>
      <p>Track your reading progress and continue your English learning journey.</p>
    </div>

    <!-- Quick Actions -->
    <div class="quick-actions">
      <el-card class="action-card" shadow="hover" @click="$router.push('/config')">
        <div class="action-content">
          <el-icon class="action-icon" color="#409eff"><Edit /></el-icon>
          <h3>Start New Exercise</h3>
          <p>Generate a new reading comprehension exercise</p>
        </div>
      </el-card>
      
      <el-card class="action-card" shadow="hover" @click="$router.push('/notes')">
        <div class="action-content">
          <el-icon class="action-icon" color="#67c23a"><Document /></el-icon>
          <h3>View Notes</h3>
          <p>Review your saved notes and translations</p>
        </div>
      </el-card>
      
      <el-card class="action-card" shadow="hover" @click="loadHistory">
        <div class="action-content">
          <el-icon class="action-icon" color="#e6a23c"><DataAnalysis /></el-icon>
          <h3>View Progress</h3>
          <p>Check your reading performance statistics</p>
        </div>
      </el-card>
    </div>

    <!-- Recent Exercises -->
    <el-card class="history-card">
      <template #header>
        <div class="card-header">
          <h2>Recent Exercises</h2>
          <el-button type="text" @click="loadHistory">View All</el-button>
        </div>
      </template>
      
      <div v-if="exerciseStore.loading" class="loading-container">
        <el-skeleton :rows="3" animated />
      </div>
      
      <div v-else-if="recentExercises.length === 0" class="empty-state">
        <el-empty description="No exercises yet">
          <el-button type="primary" @click="$router.push('/config')">
            Create Your First Exercise
          </el-button>
        </el-empty>
      </div>
      
      <div v-else class="exercise-list">
        <div
          v-for="exercise in recentExercises"
          :key="exercise.id"
          class="exercise-item"
          @click="viewExercise(exercise.id)"
        >
          <div class="exercise-info">
            <h4>{{ exercise.topic || 'General Reading' }}</h4>
            <div class="exercise-meta">
              <el-tag :type="getDifficultyType(exercise.difficulty)" size="small">
                {{ exercise.difficulty }}
              </el-tag>
              <span class="exercise-date">
                {{ formatDate(exercise.created_at) }}
              </span>
            </div>
          </div>
          <div class="exercise-stats">
            <div v-if="exercise.accuracy_rate !== null" class="accuracy">
              <span class="accuracy-label">Accuracy:</span>
              <span :class="getAccuracyClass(exercise.accuracy_rate)">
                {{ Math.round(exercise.accuracy_rate * 100) }}%
              </span>
            </div>
            <div class="word-count">
              {{ exercise.word_count }} words
            </div>
          </div>
        </div>
      </div>
    </el-card>

    <!-- Statistics -->
    <el-row :gutter="20" class="stats-row">
      <el-col :span="8">
        <el-card class="stat-card">
          <div class="stat-content">
            <div class="stat-number">{{ totalExercises }}</div>
            <div class="stat-label">Total Exercises</div>
          </div>
        </el-card>
      </el-col>
      <el-col :span="8">
        <el-card class="stat-card">
          <div class="stat-content">
            <div class="stat-number">{{ averageAccuracy }}%</div>
            <div class="stat-label">Average Accuracy</div>
          </div>
        </el-card>
      </el-col>
      <el-col :span="8">
        <el-card class="stat-card">
          <div class="stat-content">
            <div class="stat-number">{{ totalWords }}</div>
            <div class="stat-label">Words Read</div>
          </div>
        </el-card>
      </el-col>
    </el-row>
  </div>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { useAuthStore } from '../stores/auth'
import { useExerciseStore } from '../stores/exercise'
import { Edit, Document, TrendCharts } from '@element-plus/icons-vue'

const router = useRouter()
const authStore = useAuthStore()
const exerciseStore = useExerciseStore()

const recentExercises = computed(() => 
  exerciseStore.exerciseHistory.slice(0, 5)
)

const totalExercises = computed(() => 
  exerciseStore.exerciseHistory.length
)

const averageAccuracy = computed(() => {
  const completedExercises = exerciseStore.exerciseHistory.filter(
    ex => ex.accuracy_rate !== null
  )
  if (completedExercises.length === 0) return 0
  
  const sum = completedExercises.reduce((acc, ex) => acc + ex.accuracy_rate, 0)
  return Math.round((sum / completedExercises.length) * 100)
})

const totalWords = computed(() => 
  exerciseStore.exerciseHistory.reduce((acc, ex) => acc + (ex.word_count || 0), 0)
)

onMounted(() => {
  loadHistory()
})

const loadHistory = async () => {
  try {
    await exerciseStore.getExerciseHistory()
  } catch (error) {
    console.error('Failed to load exercise history:', error)
  }
}

const viewExercise = (id) => {
  router.push(`/reading/${id}`)
}

const getDifficultyType = (difficulty) => {
  const types = {
    'Beginner': 'success',
    'Intermediate': 'warning',
    'Advanced': 'danger'
  }
  return types[difficulty] || 'info'
}

const getAccuracyClass = (accuracy) => {
  if (accuracy >= 0.8) return 'accuracy-high'
  if (accuracy >= 0.6) return 'accuracy-medium'
  return 'accuracy-low'
}

const formatDate = (dateString) => {
  return new Date(dateString).toLocaleDateString('en-US', {
    month: 'short',
    day: 'numeric',
    year: 'numeric'
  })
}
</script>

<style scoped>
.dashboard {
  max-width: 1200px;
  margin: 0 auto;
}

.dashboard-header {
  text-align: center;
  margin-bottom: 40px;
}

.dashboard-header h1 {
  color: #303133;
  margin-bottom: 10px;
  font-size: 32px;
  font-weight: 600;
}

.dashboard-header p {
  color: #606266;
  font-size: 16px;
  margin: 0;
}

.quick-actions {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 20px;
  margin-bottom: 40px;
}

.action-card {
  cursor: pointer;
  transition: transform 0.2s;
}

.action-card:hover {
  transform: translateY(-4px);
}

.action-content {
  text-align: center;
  padding: 20px;
}

.action-icon {
  font-size: 48px;
  margin-bottom: 16px;
}

.action-content h3 {
  margin: 0 0 8px 0;
  color: #303133;
  font-size: 18px;
}

.action-content p {
  margin: 0;
  color: #606266;
  font-size: 14px;
}

.history-card {
  margin-bottom: 40px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.card-header h2 {
  margin: 0;
  color: #303133;
  font-size: 20px;
}

.loading-container {
  padding: 20px;
}

.empty-state {
  text-align: center;
  padding: 40px 20px;
}

.exercise-list {
  space-y: 12px;
}

.exercise-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px;
  border: 1px solid #ebeef5;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.2s;
  margin-bottom: 12px;
}

.exercise-item:hover {
  border-color: #409eff;
  background-color: #f8faff;
}

.exercise-info h4 {
  margin: 0 0 8px 0;
  color: #303133;
  font-size: 16px;
}

.exercise-meta {
  display: flex;
  align-items: center;
  gap: 12px;
}

.exercise-date {
  color: #909399;
  font-size: 14px;
}

.exercise-stats {
  text-align: right;
}

.accuracy {
  margin-bottom: 4px;
}

.accuracy-label {
  color: #606266;
  font-size: 14px;
  margin-right: 8px;
}

.accuracy-high {
  color: #67c23a;
  font-weight: 600;
}

.accuracy-medium {
  color: #e6a23c;
  font-weight: 600;
}

.accuracy-low {
  color: #f56c6c;
  font-weight: 600;
}

.word-count {
  color: #909399;
  font-size: 14px;
}

.stats-row {
  margin-top: 40px;
}

.stat-card {
  text-align: center;
}

.stat-content {
  padding: 20px;
}

.stat-number {
  font-size: 32px;
  font-weight: 600;
  color: #409eff;
  margin-bottom: 8px;
}

.stat-label {
  color: #606266;
  font-size: 14px;
}
</style>