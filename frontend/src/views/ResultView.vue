<template>
  <div class="result-container">
    <div v-if="loading" class="loading-container">
      <el-skeleton :rows="6" animated />
    </div>
    
    <div v-else-if="result" class="result-content">
      <!-- Header -->
      <div class="result-header">
        <div class="score-display">
          <div class="score-circle" :class="getScoreClass(result.total_score)">
            <span class="score-number">{{ result.total_score }}</span>
            <span class="score-label">/ 100</span>
          </div>
          <div class="score-info">
            <h1>Exercise Complete!</h1>
            <p class="accuracy">
              Accuracy: {{ Math.round(result.accuracy_rate * 100) }}%
            </p>
          </div>
        </div>
      </div>
      
      <!-- Overall Feedback -->
      <el-card class="feedback-card">
        <template #header>
          <h2>Overall Feedback</h2>
        </template>
        <div class="overall-feedback">
          {{ result.overall_feedback }}
        </div>
      </el-card>
      
      <!-- Question-by-Question Feedback -->
      <el-card class="questions-feedback-card">
        <template #header>
          <h2>Question Analysis</h2>
        </template>
        
        <div class="questions-feedback">
          <div
            v-for="(feedback, index) in result.feedback"
            :key="index"
            class="question-feedback"
          >
            <div class="question-header">
              <div class="question-number">
                Question {{ feedback.question_number }}
              </div>
              <div class="question-result">
                <el-tag
                  :type="feedback.is_correct ? 'success' : 'danger'"
                  size="large"
                >
                  {{ feedback.is_correct ? 'Correct' : 'Incorrect' }}
                </el-tag>
              </div>
            </div>
            
            <div class="answer-comparison" v-if="!feedback.is_correct">
              <div class="answer-item">
                <strong>Your Answer:</strong>
                <span class="user-answer">{{ feedback.user_answer }}</span>
              </div>
              <div class="answer-item">
                <strong>Correct Answer:</strong>
                <span class="correct-answer">{{ feedback.correct_answer }}</span>
              </div>
            </div>
            
            <div class="explanation">
              <strong>Explanation:</strong>
              <p>{{ feedback.explanation }}</p>
            </div>
          </div>
        </div>
      </el-card>
      
      <!-- Actions -->
      <div class="result-actions">
        <el-button
          type="primary"
          size="large"
          @click="$router.push('/config')"
        >
          <el-icon><Plus /></el-icon>
          New Exercise
        </el-button>
        <el-button
          size="large"
          @click="$router.push('/dashboard')"
        >
          <el-icon><House /></el-icon>
          Dashboard
        </el-button>
        <el-button
          size="large"
          @click="viewExercise"
        >
          <el-icon><View /></el-icon>
          Review Exercise
        </el-button>
      </div>
      
      <!-- Performance Chart -->
      <el-card class="chart-card">
        <template #header>
          <h2>Performance Breakdown</h2>
        </template>
        <div class="performance-stats">
          <div class="stat-item">
            <div class="stat-value correct">{{ correctCount }}</div>
            <div class="stat-label">Correct</div>
          </div>
          <div class="stat-item">
            <div class="stat-value incorrect">{{ incorrectCount }}</div>
            <div class="stat-label">Incorrect</div>
          </div>
          <div class="stat-item">
            <div class="stat-value total">{{ totalQuestions }}</div>
            <div class="stat-label">Total</div>
          </div>
        </div>
        
        <div class="progress-bar">
          <el-progress
            :percentage="Math.round(result.accuracy_rate * 100)"
            :color="getProgressColor(result.accuracy_rate)"
            :stroke-width="20"
            text-inside
          />
        </div>
      </el-card>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { useExerciseStore } from '../stores/exercise'
import { ElMessage } from 'element-plus'
import { Plus, House, View } from '@element-plus/icons-vue'

const route = useRoute()
const router = useRouter()
const exerciseStore = useExerciseStore()

const loading = ref(false)
const result = ref(null)
const exercise = ref(null)

const correctCount = computed(() => 
  result.value ? result.value.feedback.filter(f => f.is_correct).length : 0
)

const incorrectCount = computed(() => 
  result.value ? result.value.feedback.filter(f => !f.is_correct).length : 0
)

const totalQuestions = computed(() => 
  result.value ? result.value.feedback.length : 0
)

onMounted(async () => {
  await loadResult()
})

const loadResult = async () => {
  loading.value = true
  try {
    const exerciseId = route.params.id
    
    // Get exercise data
    exercise.value = await exerciseStore.getExercise(exerciseId)
    
    // For demo purposes, we'll simulate the result
    // In a real app, this would come from the backend after submission
    result.value = {
      total_score: 85,
      accuracy_rate: 0.8,
      overall_feedback: "Great job! You demonstrated strong reading comprehension skills. Focus on improving inference-based questions for even better performance.",
      feedback: exercise.value.questions.map((q, index) => ({
        question_number: index + 1,
        user_answer: "A", // This would be the actual user answer
        correct_answer: q.correct_answer,
        is_correct: Math.random() > 0.2, // Random for demo
        explanation: q.explanation
      }))
    }
  } catch (error) {
    ElMessage.error('Failed to load results')
    router.push('/dashboard')
  } finally {
    loading.value = false
  }
}

const viewExercise = () => {
  router.push(`/reading/${route.params.id}`)
}

const getScoreClass = (score) => {
  if (score >= 90) return 'excellent'
  if (score >= 80) return 'good'
  if (score >= 70) return 'fair'
  return 'needs-improvement'
}

const getProgressColor = (accuracy) => {
  if (accuracy >= 0.9) return '#67c23a'
  if (accuracy >= 0.8) return '#e6a23c'
  if (accuracy >= 0.7) return '#f56c6c'
  return '#909399'
}
</script>

<style scoped>
.result-container {
  max-width: 900px;
  margin: 0 auto;
}

.loading-container {
  padding: 40px;
}

.result-header {
  text-align: center;
  margin-bottom: 40px;
}

.score-display {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 30px;
  flex-wrap: wrap;
}

.score-circle {
  width: 120px;
  height: 120px;
  border-radius: 50%;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  border: 6px solid;
  position: relative;
}

.score-circle.excellent {
  border-color: #67c23a;
  background: linear-gradient(135deg, #67c23a20, #67c23a10);
}

.score-circle.good {
  border-color: #409eff;
  background: linear-gradient(135deg, #409eff20, #409eff10);
}

.score-circle.fair {
  border-color: #e6a23c;
  background: linear-gradient(135deg, #e6a23c20, #e6a23c10);
}

.score-circle.needs-improvement {
  border-color: #f56c6c;
  background: linear-gradient(135deg, #f56c6c20, #f56c6c10);
}

.score-number {
  font-size: 36px;
  font-weight: 700;
  color: #303133;
}

.score-label {
  font-size: 16px;
  color: #606266;
  margin-top: -4px;
}

.score-info h1 {
  margin: 0 0 8px 0;
  color: #303133;
  font-size: 28px;
  font-weight: 600;
}

.accuracy {
  margin: 0;
  color: #606266;
  font-size: 18px;
}

.feedback-card {
  margin-bottom: 30px;
}

.overall-feedback {
  font-size: 16px;
  line-height: 1.6;
  color: #303133;
  padding: 10px 0;
}

.questions-feedback-card {
  margin-bottom: 30px;
}

.questions-feedback {
  padding: 10px 0;
}

.question-feedback {
  margin-bottom: 30px;
  padding-bottom: 20px;
  border-bottom: 1px solid #ebeef5;
}

.question-feedback:last-child {
  border-bottom: none;
  margin-bottom: 0;
}

.question-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.question-number {
  font-weight: 600;
  color: #303133;
  font-size: 16px;
}

.answer-comparison {
  margin-bottom: 16px;
  padding: 12px;
  background-color: #fef0f0;
  border-radius: 6px;
  border-left: 4px solid #f56c6c;
}

.answer-item {
  display: flex;
  align-items: center;
  gap: 12px;
  margin-bottom: 8px;
}

.answer-item:last-child {
  margin-bottom: 0;
}

.answer-item strong {
  min-width: 120px;
  color: #303133;
  font-size: 14px;
}

.user-answer {
  color: #f56c6c;
  font-weight: 600;
}

.correct-answer {
  color: #67c23a;
  font-weight: 600;
}

.explanation {
  font-size: 14px;
}

.explanation strong {
  color: #303133;
  margin-bottom: 8px;
  display: block;
}

.explanation p {
  margin: 0;
  color: #606266;
  line-height: 1.6;
}

.result-actions {
  display: flex;
  justify-content: center;
  gap: 16px;
  margin-bottom: 40px;
  flex-wrap: wrap;
}

.chart-card {
  margin-bottom: 30px;
}

.performance-stats {
  display: flex;
  justify-content: space-around;
  margin-bottom: 30px;
  text-align: center;
}

.stat-item {
  flex: 1;
}

.stat-value {
  font-size: 32px;
  font-weight: 700;
  margin-bottom: 8px;
}

.stat-value.correct {
  color: #67c23a;
}

.stat-value.incorrect {
  color: #f56c6c;
}

.stat-value.total {
  color: #409eff;
}

.stat-label {
  color: #606266;
  font-size: 14px;
  font-weight: 500;
}

.progress-bar {
  margin-top: 20px;
}

/* Responsive */
@media (max-width: 768px) {
  .result-container {
    padding: 0 10px;
  }
  
  .score-display {
    flex-direction: column;
    gap: 20px;
  }
  
  .score-circle {
    width: 100px;
    height: 100px;
  }
  
  .score-number {
    font-size: 28px;
  }
  
  .question-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 8px;
  }
  
  .result-actions {
    flex-direction: column;
  }
  
  .performance-stats {
    flex-direction: column;
    gap: 20px;
  }
}
</style>